<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-12 22:20:49
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-09-17 22:22:11
 * @FilePath: /website/productIntro/index.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!DOCTYPE html>
<html lang="zh-CN">
<script src="vue.js"></script>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>唯优众宠</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }

        .image-container {
            width: 100%;
            background: white;
            padding: 0;
            /* 为底部控制栏预留空间 */
        }

        .main-image {
            width: 100%;
            height: auto;
            display: block;
            object-fit: cover;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .image-container {
                /* 移动端调整底部间距 */
            }
        }

        @media (max-width: 480px) {
            .image-container {
                /* 小屏幕进一步调整 */
            }
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="image-container">
            <img v-for="item in imgList" :src="item" class="main-image">
        </div>
    </div>
    <script>
        var app = new Vue({
            el: '#app',
            data() {
                return {
                    imgList: [
                        'http://cdn.weiyouzhongchong.cn/productIntro/01.jpg',
                        'http://cdn.weiyouzhongchong.cn/productIntro/02.jpg',
                        'http://cdn.weiyouzhongchong.cn/productIntro/03.jpg',
                        'http://cdn.weiyouzhongchong.cn/productIntro/04.jpg',
                        'http://cdn.weiyouzhongchong.cn/productIntro/05.jpg',
                        'http://cdn.weiyouzhongchong.cn/productIntro/06.jpg',
                        'http://cdn.weiyouzhongchong.cn/productIntro/07.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/08.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/09.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/10.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/11.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/12.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/13.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/14.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/15.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/16.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/17.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/18.jpg',       
                        'http://cdn.weiyouzhongchong.cn/productIntro/19.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/20.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/21.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/22.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/23.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/24.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/25.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/26.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/27.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/28.jpg',   
                        'http://cdn.weiyouzhongchong.cn/productIntro/29.jpg',   
                    ]
                };
            }
        })
    </script>
</body>

</html>