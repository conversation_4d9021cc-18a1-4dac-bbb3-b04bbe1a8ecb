<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>唯优众宠</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }

        .image-container {
            width: 100%;
            background: white;
            padding: 0;
            padding-bottom: 160px;
            /* 为底部控制栏预留空间 */
        }

        .main-image {
            width: 100%;
            height: auto;
            display: block;
            object-fit: cover;
        }

        .controls-container {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            padding: 12px;
            padding-bottom: 20px;
            z-index: 100;
        }

        .container {
            max-width: 500px;
            margin: 0 auto;
            text-align: center;
        }

        .title {
            font-size: 20px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 6px;
            letter-spacing: -0.5px;
        }

        .subtitle {
            font-size: 13px;
            color: #7f8c8d;
            margin-bottom: 12px;
            font-weight: 400;
            line-height: 1.2;
        }

        .download-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 14px 32px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            margin-bottom: 10px;
            width: 100%;
            max-width: 280px;
        }

        .download-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
        }

        .download-btn:active {
            transform: translateY(-1px);
        }

        .download-btn.disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .countdown {
            color: #667eea;
            font-size: 13px;
            margin-bottom: 10px;
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #e0e0e0;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .wechat-tip {
            display: none;
            background: #fff3cd;
            border: 1px solid #f39c12;
            border-radius: 10px;
            padding: 12px;
            margin-top: 8px;
            box-shadow: 0 2px 8px rgba(243, 156, 18, 0.1);
        }

        .wechat-tip.show {
            display: block;
            animation: slideIn 0.4s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .wechat-tip h3 {
            color: #d68910;
            margin-bottom: 8px;
            font-size: 15px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .wechat-tip p {
            color: #b7950b;
            font-size: 13px;
            line-height: 1.3;
            margin-bottom: 8px;
        }

        .steps {
            text-align: left;
            margin-top: 6px;
            padding-left: 14px;
        }

        .steps li {
            margin-bottom: 6px;
            color: #b7950b;
            font-size: 12px;
            line-height: 1.2;
        }

        .browser-info {
            color: #6c757d;
            font-size: 11px;
            margin-top: 8px;
            padding: 6px 12px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 1px solid #dee2e6;
        }

        .device-icon {
            font-size: 14px;
            margin-right: 6px;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .image-container {
                padding-bottom: 140px;
                /* 移动端调整底部间距 */
            }

            .controls-container {
                padding: 10px;
                padding-bottom: 16px;
            }
        }

        @media (max-width: 480px) {
            .image-container {
                padding-bottom: 120px;
                /* 小屏幕进一步调整 */
            }

            .controls-container {
                padding: 8px;
                padding-bottom: 14px;
            }

            .title {
                font-size: 18px;
            }

            .subtitle {
                font-size: 12px;
            }

            .download-btn {
                font-size: 16px;
                padding: 14px 32px;
            }
        }
    </style>
</head>

<body>
    <!-- 图片展示区域 -->
    <div class="image-container">
        <img src="img/guanyuwomen.png" alt="关于我们" class="main-image"
            onerror="this.style.display='none'; this.parentNode.innerHTML='<div style=\'padding:40px;text-align:center;color:#666;\'>图片加载失败</div>'">
    </div>

    <!-- 底部控制区域 -->
    <div class="controls-container">
        <div class="container">
            <!-- <div class="countdown" id="countdown" style="display: none;">
                <div class="loading"></div>
                <span id="countdownText">3秒后自动跳转...</span>
            </div> -->

            <button class="download-btn" id="downloadBtn">
                <span id="btnText">立即前往</span>
            </button>

            <div class="wechat-tip" id="wechatTip">
                <h3>
                    <span>⚠️</span>
                    请在手机浏览器中打开
                </h3>
                <p>为了更好的使用体验，请在浏览器中打开此页面：</p>
                <ol class="steps">
                    <li>点击右上角的 <strong>"···"</strong> 菜单按钮</li>
                    <li>选择 <strong>"在浏览器中打开"</strong> 或 <strong>"用Safari打开"</strong>即可正常前往唯优众宠</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // 配置下载链接 - 请替换为实际的下载链接
        const DOWNLOAD_LINKS = {
            ios: 'https://apps.apple.com/cn/app/%E5%94%AF%E4%BC%98%E4%BC%97%E5%AE%A0/id6745561458', // 替换为实际的App Store链接
            android: 'https://m.malink.cn/s/BZFVna', // 替换为实际的Android下载链接
            default: 'https://m.malink.cn/s/BZFVna' // 默认跳转链接
        };

        // 检测设备和浏览器环境
        function detectEnvironment() {
            const userAgent = navigator.userAgent.toLowerCase();
            const isIOS = /iphone|ipad|ipod/.test(userAgent);
            const isAndroid = /android/.test(userAgent);
            const isWeChat = /micromessenger/.test(userAgent);
            const isMobile = /mobile|android|iphone|ipad|phone/.test(userAgent);

            return {
                isIOS,
                isAndroid,
                isWeChat,
                isMobile,
                userAgent
            };
        }

        // 获取设备图标
        function getDeviceIcon(env) {
            if (env.isIOS) return '📱';
            if (env.isAndroid) return '🤖';
            if (env.isMobile) return '📱';
            return '💻';
        }

        // 更新页面显示
        function updatePageDisplay(env) {
            const downloadBtn = document.getElementById('downloadBtn');
            const btnText = document.getElementById('btnText');
            const wechatTip = document.getElementById('wechatTip');
            const countdown = document.getElementById('countdown');
            const imageContainer = document.querySelector('.image-container');

            let deviceType = '桌面端浏览器';
            let buttonText = '立即下载';

            if (env.isIOS) {
                deviceType = 'iOS设备';
                buttonText = '前往 App Store 下载';
            } else if (env.isAndroid) {
                deviceType = 'Android设备';
                buttonText = '下载 Android 版本';
            } else if (env.isMobile) {
                deviceType = '移动设备';
            }

            if (env.isWeChat) {
                deviceType += ' (微信浏览器)';
                wechatTip.classList.add('show');
                downloadBtn.classList.add('disabled');
                buttonText = '请在手机浏览器中打开';

                // 微信环境下增加图片底部间距，避免被提示框遮挡
                if (window.innerWidth <= 480) {
                    imageContainer.style.paddingBottom = '200px';
                } else if (window.innerWidth <= 768) {
                    imageContainer.style.paddingBottom = '220px';
                } else {
                    imageContainer.style.paddingBottom = '240px';
                }
            } else {
                // 非微信环境显示倒计时
                countdown.style.display = 'block';
                // startCountdown();

                // 非微信环境使用默认间距
                if (window.innerWidth <= 480) {
                    imageContainer.style.paddingBottom = '120px';
                } else if (window.innerWidth <= 768) {
                    imageContainer.style.paddingBottom = '140px';
                } else {
                    imageContainer.style.paddingBottom = '160px';
                }
            }

            const deviceIcon = getDeviceIcon(env);
            btnText.textContent = buttonText;
        }

        // 倒计时功能
        function startCountdown() {
            //let count = 3;
            //const countdownText = document.getElementById('countdownText');

            //const timer = setInterval(() => {
            //    count--;
            //    if (count > 0) {
            //        countdownText.textContent = `${count}秒后自动跳转...`;
            //    } else {
            //        clearInterval(timer);
            //        const env = detectEnvironment();
            //        handleDownload(env);
            //    }
            //}, 1000);
        }

        // 处理下载点击
        function handleDownload(env) {
            if (env.isWeChat) {
                // 微信环境下不执行跳转，只显示提示
                return;
            }

            let downloadUrl = DOWNLOAD_LINKS.default;

            if (env.isIOS) {
                downloadUrl = DOWNLOAD_LINKS.ios;
            } else if (env.isAndroid) {
                downloadUrl = DOWNLOAD_LINKS.android;
            }

            // 跳转到对应的下载链接
            window.location.href = downloadUrl;
        }

        // 初始化页面
        function init() {
            const env = detectEnvironment();
            updatePageDisplay(env);

            // 绑定下载按钮点击事件
            document.getElementById('downloadBtn').addEventListener('click', () => {
                handleDownload(env);
            });

            // 监听窗口大小变化，重新调整间距
            window.addEventListener('resize', () => {
                updatePageDisplay(env);
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>

</html>