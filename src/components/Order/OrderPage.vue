<template>
  <div class="content-page">
    <div class="content-nav">
      <el-breadcrumb class="breadcrumb" separator="/">
        <el-breadcrumb-item>订单列表</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="content-main">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="待发货" name="1"></el-tab-pane>
        <el-tab-pane label="已发货" name="2"></el-tab-pane>
        <el-tab-pane label="申请退款" name="3"></el-tab-pane>
        <el-tab-pane label="全部订单" name="4"></el-tab-pane>
      </el-tabs>
      <div class="filter-box">
        <el-form :inline="true" :model="filterForm" class="demo-form-inline">
          <el-form-item label="订单号">
            <el-input
              class="filter-input"
              v-model="filterForm.order_sn"
              placeholder="请输入订单号"
            ></el-input>
          </el-form-item>
          <el-form-item label="收货人">
            <el-input
              class="filter-input"
              v-model="filterForm.consignee"
              placeholder="请输入收货人姓名/联系电话"
            ></el-input>
          </el-form-item>
          <el-form-item label="快递号">
            <el-input
              class="filter-input"
              v-model="filterForm.logistic_code"
              placeholder="请输入快递单号"
            ></el-input>
          </el-form-item>
          <el-form-item label="用户">
            <el-input
              class="filter-input"
              v-model="filterForm.user_mobile"
              placeholder="请输入用户手机号"
            ></el-input>
          </el-form-item>
          <el-form-item label="商品名称">
            <el-input
              class="filter-input"
              v-model="filterForm.keywords"
              placeholder="请输入商品名称"
            ></el-input>
          </el-form-item>
          <!-- 状态选择 -->
           <el-form-item label="状态" v-if="activeName == '4'">
            <!--   `order_status` 101：未付款、102：已取消、103已取消(系统)、201：已付款、202：申请订单取消，退款中、
               203：已退款、204：拒绝退款、205：同意退款，等待退货，301：已发货、302：已收货、303：已收货(系统)、
               401：已完成、801：拼团中,未付款、802：拼团中,已付款',
            -->
            <el-select v-model="filterForm.order_status" placeholder="请选择">
              <el-option label="全部" value=""></el-option>
              <el-option label="待付款" :value="'101'"></el-option>
              <el-option label="已取消" :value="'102,103'"></el-option>
              <el-option label="待发货" :value="'201,300'"></el-option>
              <el-option label="申请售后" :value="'202,205'"></el-option>
              <el-option label="已退款" :value="'203'"></el-option>
              <el-option label="拒绝退款" :value="'204'"></el-option>
              <el-option label="已发货" :value="'301,302,303'"></el-option>
              <el-option label="已完成" :value="'401'"></el-option>
            </el-select>
          </el-form-item>
          <!-- 订单时间 -->
          <div>
            <el-form-item label="下单时间">
              <el-date-picker
                v-model="filterForm.startTime"
                type="datetime"
                placeholder="选择开始时间"
                default-time="00:00:00"
                style="width: 240px;"
              >
              </el-date-picker>
              <span style="margin-left: 10px; margin-right: 10px;"> 至 </span>
              <el-date-picker
                v-model="filterForm.endTime"
                type="datetime"
                placeholder="选择结束时间"
                default-time="23:59:59"
                style="width: 240px;"
              >
              </el-date-picker>
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSubmitFilter">查询</el-button>
          </el-form-item>
          <!-- 批量发货 -->
          <el-button v-if="activeName == '1'" style="margin-bottom: 10px; margin-left: 30px;" type="primary" @click="batchDelivery">批量发货</el-button>
        </el-form>
      </div>
      <div class="form-table-box">
        <el-checkbox-group
          v-model="batchList"
          @change="handleBatchCheckedChange"
        >
          <div v-for="item in tableData" class="list-wrap clearfix">
            <div class="header clearfix">
              <div class="left">
                <div class="off-text" v-if="item.offline_pay">线下支付订单</div>
                <div class="status-text">{{ item.order_status_text }}</div>
                <div class="add-time">下单时间：{{ item.create_time }}</div>
                <div class="pay-time" v-if="item.pay_time != 0">
                  付款时间：{{ item.pay_time }}
                </div>
                <div class="pay-time" v-if="item.shipping_time != 0">
                  发货时间：{{ item.shipping_time }}
                </div>
                <div class="order-id">订单号：{{ item.order_sn }}</div>

                <div class="goods-num" style="margin-left: 20px;">共{{ item.goodsCount }}件商品</div>
              </div>

              <el-checkbox v-if="activeName == '1'" :label="item.id" class="box-check">{{ '选择' }}</el-checkbox>
            </div>
            <div class="content-wrap clearfix">
              <div class="left">
                <div class="goods-list" v-for="iitem in item.goodsList">
                  <img :src="iitem.list_pic_url" class="goods-img" />
                  <div>
                    <div class="goods-name">{{ iitem.goods_aka }}</div>
                    <div class="goods-name">【{{iitem.goods_specifition_name}}】</div>
                  </div>
                  <div class="goods-number">
                    <label>数量：</label>{{ iitem.number }}
                  </div>
                </div>
              </div>
              <div class="user-wrap">
                <div class="avatar-wrap">
                  <el-image :src="item.userInfo.avatar" class="avatar-img" >
                    <div slot="error" class="image-slot">
                      <img :src="defaultAvatar" class="avatar-img" />
                    </div>
                  </el-image>
                  <div class="nickname">{{ item.userInfo.nickname }}</div>
                </div>
                <!-- <div class="name">姓名：{{ item.userInfo.name }}</div> -->
                <div class="mobile">手机：{{ item.userInfo.mobile }}</div>
              </div>
              <div class="main">
                <div v-if="item.expressInfo != ''" class="express-info">
                  {{ item.expressInfo }}
                </div>
                <div class="m1">
                  <div class="user-name">{{ item.consignee }}</div>
                  <div class="user-mobile">{{ item.mobile }}</div>
                </div>
                <div class="user-address">
                  {{ item.full_region }}{{ item.address }}
                </div>
                <div v-if="item.postscript != ''" class="user-post">
                  留言：{{ item.postscript }}
                </div>
                <el-input
                  class="admin-memo"
                  type="textarea"
                  @blur="changeMemo(item.id, item.admin_memo)"
                  v-model="item.admin_memo"
                  placeholder="备注"
                ></el-input>
              </div>
              <div class="right">
                <el-button
                  v-if="item.order_status == 300 || item.order_status == 201"
                  class="d-btn"
                  type="primary"
                  @click="orderEdit(item)"
                  size="middle"
                  >发货
                </el-button>
                <!--   `order_status` 101：未付款、102：已取消、103已取消(系统)、201：已付款、202：申请订单取消，退款中、
                  203：已退款、204：拒绝退款、205：同意退款，等待退货，300 备货未发 301：已发货、302：已收货、303：已收货(系统)、
                  401：已完成、801：拼团中,未付款、802：拼团中,已付款',
                -->
                <div v-if="item.order_status == 201 || item.order_status == 202 || item.order_status == 204 || 
                  item.order_status == 300 || item.order_status == 301 || item.order_status == 302 || 
                  item.order_status == 303 || item.order_status == 401">
                  <el-button
                    size="mini"
                    @click="handleForceRefundOrder(item)"
                    type="danger"
                    style="margin-bottom: 10px;"
                    >强制退款
                  </el-button>

                  <el-button
                    size="mini"
                    @click="handlePartialRefundOrder(item)"
                    type="danger"
                    >部分退款
                  </el-button>
                </div>
                <el-button
                  v-if="item.order_status == 101"
                  size="mini"
                  @click="orderEdit(item)"
                  >修改价格
                </el-button>
                <div v-if="item.order_status >= 301">
                  <el-button
                    v-if="item.order_status == 301"
                    size="mini"
                    @click="orderEdit(item)"
                    >打印快递单
                  </el-button>
                  <el-button
                    style="margin-top: 3px;"
                    v-if="item.order_status >= 301"
                    size="mini"
                    @click="showOrderExpress(item)"
                    >查看快递
                  </el-button>
                </div>
                <div class="flex-col" v-else-if="item.order_status == 202">
                  <div class="flex-row">
                    <el-button
                      v-if="item.expressInfo"
                      size="mini"
                      @click="handleRefundOrder(item, true)"
                      type="danger"
                      >同意退款，等待退货
                    </el-button>
                    <el-button
                      size="mini"
                      @click="handlePartialRefundOrder(item)"
                      type="danger"
                      >部分退款
                    </el-button>
                  </div>
                  <el-button
                    class="right-detail"
                    size="mini"
                    @click="handleRefundOrder(item, false)"
                    type="primary"
                    >拒绝退款
                  </el-button>
                </div>
                <!-- 101：未付款、102：已取消、103已取消(系统)、201：已付款、202：申请订单取消，退款中、
                 203：已退款、204：拒绝退款、205：同意退款，等待退货，
                 301：已发货、302：已收货、303：已收货(系统)、401：已完成、
                 801：拼团中,未付款、802：拼团中,已付款 -->
                <div v-else-if="item.order_status == 205">
                  <el-button
                    size="mini"
                    @click="refundOrder(item, true)"
                    type="danger"
                    >已收货，完成退款
                  </el-button>
                  <el-button
                      size="mini"
                      @click="handlePartialRefundOrder(item)"
                      type="danger"
                      >部分退款
                  </el-button>
                </div>

                <el-button
                  class="right-detail"
                  type="text"
                  @click="viewDetail(item.id)"
                  size="mini"
                >
                  查看详情
                </el-button>
              </div>
            </div>
            <div class="header clearfix">
              <div class="right">
                <div class="price-wrap">
                  当前合计{{ item.actual_price }}元（运费：{{ item.freightTemplateTitle || '' }} ¥{{
                    item.freight_price
                  }}元）
                </div>
                <div
                  v-if="item.goods_discount_price"
                  class="price-change"
                >
                  已改价优惠：￥{{ item.goods_discount_price }}元
                </div>
                <div class="price-change" v-if="item.coupon_code">优惠券金额：￥{{item.coupon_discount}}（{{item.coupon_name}}）</div>
              </div>
            </div>
          </div>
        </el-checkbox-group>
      </div>
      <div class="page-box">
        <el-pagination
          @current-change="handlePageChange"
          :current-page="page"
          :page-size="10"
          layout="total, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
      <span>确定打包备货</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="提示" :visible.sync="dialogVisible2">
      <span>确定收货？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible2 = false">取 消</el-button>
        <el-button type="primary" @click="receiveConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="打印快递单" :visible.sync="dialogFormVisible">
      <el-form :model="dform">
        <div class="dialog-wrap">
          <div class="list-wrap">
            <div class="goods-list" v-for="ditem in orderInfo.goodsList">
              <img :src="ditem.list_pic_url" class="goods-img" />
              <div class="goods-name">{{ ditem.goods_name }}</div>
              <div class="goods-name">{{ ditem.goods_aka }}</div>
              <div class="goods-spec">
                {{ ditem.goods_specifition_name }}
              </div>
              <div class="goods-number">
                <label>数量：</label>{{ ditem.number }}
              </div>
            </div>
          </div>
          <div class="dialog-main" v-if="dform.method == 1">
            <div class="l">
              <div class="title">寄件人</div>
              <div class="detail">
                <div class="m1">
                  <div class="user-name">
                    <el-input
                      size="mini"
                      class="senderInput"
                      v-model="sender.name"
                      placeholder="寄件人姓名"
                    ></el-input>
                  </div>
                  <div class="user-mobile">
                    <el-input
                      size="mini"
                      class="senderInput"
                      v-model="sender.mobile"
                      placeholder="寄件人手机"
                    ></el-input>
                  </div>
                </div>
                <div class="user-address">
                  <el-cascader
                    style="width: 200px"
                    size="mini"
                    :options="options"
                    placeholder="请选择地区"
                    v-model="senderOptions"
                    @change="handleSenderProvinceChange"
                  >
                  </el-cascader>
                  <el-input
                    size="mini"
                    class="address-input"
                    v-model="sender.address"
                    auto-complete="off"
                    placeholder="请输入详细地"
                  ></el-input>
                </div>
              </div>
            </div>
          </div>
          <div class="dialog-main">
            <div class="l">
              <div class="title">收件人</div>
              <div class="detail">
                <div class="m1">
                  <div class="user-name">
                    <el-input
                      size="mini"
                      class="senderInput"
                      v-model="receiver.name"
                      placeholder="收件人姓名"
                    ></el-input>
                  </div>
                  <div class="user-mobile">
                    <el-input
                      size="mini"
                      class="senderInput"
                      v-model="receiver.mobile"
                      placeholder="收件人手机"
                    ></el-input>
                  </div>
                </div>
                <div class="user-address">
                  <el-cascader
                    style="width: 200px"
                    size="mini"
                    :options="options"
                    placeholder="请选择地区"
                    v-model="receiveOptions"
                    @change="handleReceiverProvinceChange"
                  >
                  </el-cascader>
                  <el-input
                    size="mini"
                    class="address-input"
                    v-model="receiver.address"
                    auto-complete="off"
                    placeholder="请输入详细地"
                  ></el-input>
                </div>
              </div>
            </div>
          </div>
          <div class="dialog-main" style="padding-top: 10px; padding-bottom: 10px; justify-content: flex-start;">
            <span style="font-weight: bold;">订单快递：</span>
            <span style="color: tomato;">{{ orderInfo.freightTemplateTitle }}</span>
          </div>
          <div v-if="orderInfo.postscript != ''" class="user-post-t">
            买家留言：{{ orderInfo.postscript }}
          </div>
          <div
            v-if="orderInfo.admin_memo != '' && orderInfo.admin_memo != null"
            class="user-admin-t"
          >
            备注：{{ orderInfo.admin_memo }}
          </div>
        </div>
        <el-form-item label="类型" style="margin-top: 10px">
          <el-radio-group
            v-model="dform.method"
            @change="deliveryMethodChange(dform.method)"
          >
            <el-radio :label="1">快递</el-radio>
            <el-radio v-if="(orderInfo.order_status == 300 || orderInfo.order_status == 201)" :label="2">手动输入快递</el-radio>
            <!-- <el-radio :label="3">自提件</el-radio> -->
          </el-radio-group>
        </el-form-item>
        <template v-if="dform.method == 1">
          <el-form-item label="选择快递" >
            <el-radio-group v-model="expressCode" @input="changeExpressCode">
              <el-radio v-for="item in shipperList" :key="item.code" :label="item.code">{{ item.name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item
            label="要在快递单上打印单发货内容"
            v-if="dform.method == 1"
          >
            <el-input
              type="textarea"
              v-model="orderInfo.print_info"
              @blur="changeInfo(orderInfo)"
              placeholder="请输入发货信息"
            ></el-input>
          </el-form-item>
        </template>
        
        <el-form-item required label="快递单号" v-if="dform.method == 2">
          <el-input style="margin-left: 30px;" v-model="dform.logistic_code"></el-input>
        </el-form-item>

        <el-form-item required label="选择快递" v-if="dform.method == 2">
          <el-select
            style="margin-left: 30px;"
            v-model="nowDeliveryId"
            value-key="code"
            placeholder="请选择快递"
          >
            <el-option
              v-for="item in shipperList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer print-footer">
        <div class="f-right" v-if="dform.method != 1">
          <el-button style="margin-right: 20px" @click="hidePrintDialog"
            >取 消</el-button
          >
          <el-button type="primary" @click="deliveryGoGo">发货</el-button>
        </div>
        <div class="f-right" v-if="dform.method == 1">
          <el-button style="margin-right: 20px" @click="hidePrintDialog"
            >取 消</el-button
          >
          <div>
            <el-button
              v-if="(orderInfo.order_status == 300 || orderInfo.order_status == 201)"
              type="primary"
              @click="createdOrderExpressResp()"
              >打印快递单并发货</el-button
            >
            <!-- 已发货可以补打 -->
            <el-button
              v-if="orderInfo.order_status == 301"
              type="primary"
              @click="supplementExpressResp()"
              >补打快递单</el-button
            >
          </div>
        </div>
      </div>
    </el-dialog>

    <el-dialog title="修改价格" :visible.sync="dialogPriceVisible">
      <el-form :model="orderInfo">
        <!-- <el-form-item label-width="120px" label="修改商品价格:">
          <el-input-number
            @change="goodsPriceChange"
            :min="0"
            :max="99999999"
            v-model="orderInfo.goods_price"
            auto-complete="off"
            placeholder="请输入商品价格"
          ></el-input-number>
        </el-form-item> -->
        <el-form-item label-width="120px" label="商品优惠金额:">
          <el-input-number
            @change="goodsPriceChange"
            :min="0"
            :max="orderInfo.goods_price"
            v-model="orderInfo.goods_discount_price"
            auto-complete="off"
            placeholder="请输入商品优惠金额"
          ></el-input-number>
        </el-form-item>
        <el-form-item label-width="120px" label="快递价格:">
          <el-input-number
            @change="freightPriceChange"
            :min="0"
            :max="99999999"
            v-model="orderInfo.freight_price"
            disabled
            auto-complete="off"
            placeholder="请输入修改后的快递"
          ></el-input-number>
        </el-form-item>
        <el-form-item label-width="120px" label="优惠券金额:">
          <el-input-number
            v-model="orderInfo.coupon_discount"
            disabled
            auto-complete="off"
          ></el-input-number>
        </el-form-item>

        <el-form-item label="商品总金额" label-width="120px">
          <h2>¥{{ Number(orderInfo.goods_price) - Number(orderInfo.goods_discount_price) }}</h2>
        </el-form-item>

        <el-form-item label-width="120px" label="改价后总价:">
          <h2>¥{{ Number(orderInfo.goods_price) +
        Number(orderInfo.freight_price) - Number(orderInfo.coupon_discount) - Number(orderInfo.goods_discount_price) }}</h2>
        </el-form-item>
        
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogPriceVisible = false">取 消</el-button>
        <el-button type="primary" @click="priceChangeConfirm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="批量发货" :visible.sync="showBatchDelivery">
      <el-form>
        <el-form-item label="选择快递" >
          <el-radio-group v-model="expressCode" @input="changeExpressCode">
            <el-radio v-for="item in shipperList" :key="item.code" :label="item.code">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <div v-for="orderItem in batchOrderList" :key="orderItem.orderInfo.id">
          <el-form-item :label="`${orderItem.orderInfo.order_sn} 快递单上打印单发货内容`">
            <el-input
              :value="orderItem.orderInfo.print_info"
              type="textarea"
              @change="changeBatchInfo(index, orderItem.orderInfo.print_info)"
              placeholder="请输入发货信息"
            ></el-input>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showBatchDelivery = false">取 消</el-button>
        <el-button type="primary" @click="batchDeliveryConfirm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="查看快递" :visible.sync="dialogOrderExpressVisible"> 
      <!-- 快递单列表 -->
      <div class="flex-row" style="border-bottom: 1px solid #d1dbe5; padding-bottom: 10px; padding-top: 10px;" v-for="(item, index) in orderExpressList" :key="item.id"> 
        <div>
          <span>快递公司：</span>
          <el-select :disabled="!item.isEdit" style="margin-left: 10px; width: 160px;" v-model="item.shipper_code" placeholder="请选择快递公司">
            <el-option
              v-for="item in shipperList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
            </el-option>
          </el-select>

          <span style="margin-left: 20px;">{{ item.express_type == 1 ? '快递单号' : '退货单号' }}</span>
          <el-input :disabled="!item.isEdit" style="margin-left: 10px; width: 200px;" v-model="item.logistic_code" placeholder="请输入快递单号"></el-input>
        </div>
        <el-button size="mini" v-if="item.express_type == 1" @click="saveEditOrderExpress(item)" style="margin-left: 20px;" type="primary">{{ item.isEdit ? '保存' : '编辑' }}</el-button>
        <el-button size="mini" v-if="index !== 0" @click="deleteOrderExpress(item)" style="margin-left: 20px;" type="danger">删除</el-button>
        <el-button size="mini" v-if="index === orderExpressList.length - 1" @click="addEditOrderExpress" style="margin-left: 20px;" type="primary">新增</el-button>
      </div>
    </el-dialog>

    <!-- 退款对话框 -->
    <RefundDialog
      :visible.sync="refundDialogVisible"
      :mode="refundMode"
      :order-info="currentRefundOrder"
      @confirm="onRefundConfirm"
      @cancel="onRefundCancel"
    />
  </div>
</template>

<script>
import VueBarcode from "../../../node_modules/vue-barcode";
import ElButton from "../../../node_modules/element-ui/packages/button/src/button.vue";
// Vue.component(VueBarcode.name, VueBarcode);
import defaultAvatar from '@/assets/default_icon.png';
import RefundDialog from './RefundDialog.vue';
// import { Button } from 'element-ui';
export default {
  data() {
    return {
      defaultAvatar,
      sfHasValue: {},
      barcodeValue: "test",
      printMiandan: false,
      rawHtml: "",
      expressType: "", // 选择快递面单模板
      expressCode: "", // 选择快递公司
      partnerId: "", // 快递客户id
      checkAll: false,
      checkedCities: ["上海", "北京"],
      cities: ["上海", "北京", "广州", "深圳"],
      isIndeterminate: true,
      page: 1,
      total: 0,
      filterForm: {
        name: "",
        logistic_code: "",
        order_status: "",
        startTime: "",
        endTime: "",
        user_mobile: "",
        keywords: "",
      },
      tableData: [],
      activeName: "1",
      order_status: "201,300",
      dialogVisible: false,
      dialogVisible2: false,
      dialogFormVisible: false,
      dialogPriceVisible: false,
      dialogText: "",
      dialogIndex: 0,
      order_sn: 0,
      order_id: 0,
      dform: {
        method: 1,
      },
      orderInfo: {},
      isShow: true,
      nowDeliveryId: "",
      formLabelWidth: "120px",
      barcode_option: {
        displayValue: false, //是否默认显示条形码数据
        background: "#fff", //条形码背景颜色
        width: 2,
        height: 100,
        fontSize: 20, //字体大小
      },
      senderInfo: {},
      receiverInfo: {},
      rePrintStatus: 0,
      changeSender: 0,
      changeReceive: 0,
      options: [],
      senderOptions: [],
      receiveOptions: [],
      receiver: {},
      sender: {},

      shipperList: [],

      batchList: [],
      batchOrderList: [],
      showBatchDelivery: false,
      dialogOrderExpressVisible: false,
      orderExpressList: [],

      // 退款对话框相关
      refundDialogVisible: false,
      refundMode: '', // 'force' 或 'partial'
      currentRefundOrder: null
    };
  },
  methods: {
    hidePrintDialog() {
      this.dform.method = 2;
      this.dialogFormVisible = false;
      console.log("11111");
    },
    // goodsPriceChange(value) {
    //   console.log(value);
    //   this.orderInfo.goods_price = value;
    //   this.orderInfo.actual_price =
    //     Number(this.orderInfo.goods_price) +
    //     Number(this.orderInfo.freight_price) - Number(this.orderInfo.coupon_discount) - Number(this.orderInfo.goods_discount_price);
    // },
    goodsPriceChange(value) {
      this.orderInfo.goods_discount_price = value;
    },
    freightPriceChange(value) {
      this.orderInfo.freight_price = value;
      this.orderInfo.actual_price =
        Number(this.orderInfo.goods_price) + Number(value);
    },
    getAllRegion() {
      let that = this;
      this.axios.get("order/getAllRegion").then((response) => {
        this.options = response.data.data;
      });

      // 获取setting
      this.axios.get('shipper').then((response) => {
          if (response.data.errno === 0) {
            this.shipperList = response.data.data.info;
          }
      })
    },

    confirm() {
      this.axios
        .get("order/orderpack", {
          params: {
            orderId: this.order_id,
          },
        })
        .then((response) => {
          this.dialogVisible = false;
          this.getList();
        });
    },
    changeRemarkInfo(info) {
      this.axios
        .post("order/saveRemarkInfo", {
          remark: info.remark,
          id: info.id,
        })
        .then((response) => {
          if (response.data.errno === 0) {
            this.$message({
              type: "success",
              message: "保存成功!",
            });
          } else {
            this.$message({
              type: "error",
              message: "保存失败",
            });
          }
        });
    },
    changeInfo(info) {
      let id = info.id;
      let print_info = info.print_info;
      this.axios
        .post("order/savePrintInfo", {
          print_info: print_info,
          id: id,
        })
        .then((response) => {
          if (response.data.errno === 0) {
            this.$message({
              type: "success",
              message: "保存成功!",
            });
          } else {
            this.$message({
              type: "error",
              message: "保存失败",
            });
          }
        });
    },
    changeMemo(id, text) {
      this.axios
        .post("order/saveAdminMemo", {
          text: text,
          id: id,
        })
        .then((response) => {
          if (response.data.errno === 0) {
            this.$message({
              type: "success",
              message: "保存成功!",
            });
          } else {
            this.$message({
              type: "error",
              message: "保存失败",
            });
          }
        });
    },
    cancelPrint() {
      this.printMiandan = false;
      this.dialogFormVisible = false;
    },

    viewDetail(index) {
      this.$router.push({
        name: "order_detail",
        query: {
          id: index,
        },
      });
    },
    handleClick(tab, event) {
      let pindex = tab._data.index;
      this.batchList = [];
      this.batchOrderList = [];

      if (pindex == 0) {
        // 待发货 order_status = '201,300';
        this.order_status = '201,300';
      } else if (pindex == 1) {
        // 已发货 rder_status = '301,302,303';
        this.order_status = '301,302,303';
      } else if (pindex == 2) {
        // 申请退款 order_status = '202';
        this.order_status = '202';
      } else {
        // 全部订单 order_status = '';
        this.order_status = '';
      }
      this.getList();
    },

    handlePageChange(val) {
      this.page = val;
      //保存到localStorage
      localStorage.setItem("orderPage", this.page);
      localStorage.setItem("orderFilterForm", JSON.stringify(this.filterForm));
      this.getList();
    },
    handleRowEdit(index, row) {
      this.$router.push({
        name: "order_detail",
        query: {
          id: row.id,
        },
      });
    },
    handleRowDelete(index, row) {
      this.$confirm("确定要删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.axios
          .post("order/destory", {
            id: row.id,
          })
          .then((response) => {
            console.log(response.data);
            if (response.data.errno === 0) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
            }
          });
      });
    },
    onSubmitFilter() {
      this.page = 1;
      this.getList();
    },
    getList() {
      let parma = {
        page: this.page,
        orderSn: this.filterForm.order_sn,
        consignee: this.filterForm.consignee,
        logistic_code: this.filterForm.logistic_code,
        startTime: this.filterForm.startTime,
        endTime: this.filterForm.endTime,
        user_mobile: this.filterForm.user_mobile,
        keywords: this.filterForm.keywords
      };
      if (this.order_status) {
        parma.status = this.order_status;
      }else{
        parma.status = this.filterForm.order_status;
      }
      this.axios
        .get("order", {
          params: parma,
        })
        .then((response) => {
          if (response.data.errno == 0) {
            this.tableData = response.data.data.data;
            this.page = parseInt(response.data.data.currentPage);
            this.total = parseInt(response.data.data.count);
          }
        });
    },
    async orderEdit(item) {
      this.order_id = item.id;
      if (item.order_status == 201 || item.order_status == 300 || item.order_status == 301) {
        // 打印快递单
        this.checkExpressInfo();
      } else if (item.order_status == 101) {
        await this.getOrderInfo(this.order_id);
        this.dialogPriceVisible = true;
      } else if (item.order_status == 301 && item.is_fake == 1) {
        this.dialogVisible2 = true;
      }
    },
    
    receiveConfirm() {
      this.axios
        .get("order/orderReceive", {
          params: {
            orderId: this.order_id,
          },
        })
        .then((response) => {
          this.dialogVisible2 = false;
          this.getList();
        });
    },

    deliveryGoGo() {
      if (this.dform.method == 2) {
        if (this.dform.logistic_code == undefined || this.nowDeliveryId == "") {
          this.$message({
            type: "error",
            message: "快递单号，收货人手机号，快递公司均需填写！",
          });
          return false;
        }
      }
      this.axios.post("order/goDelivery", {
        order_id: this.order_id,
        logistic_code: this.dform.logistic_code,
        receiver: {
          name: this.receiver.name,
          mobile: this.receiver.mobile,
          province: this.receiver.province_id,
          city: this.receiver.city_id,
          district: this.receiver.district_id,
          address: this.receiver.address,
        },
        shipper_code: this.nowDeliveryId,
      }).then((response) => {
        if (response.data.errno === 0) {
          this.getList();
          this.$message({
            type: "success",
            message: "发货成功!",
          });
          this.dialogFormVisible = false;
        }else{
          this.$message({
            type: "error",
            message: "发货失败!",
          });
        }
      })
    },

    // /**
    //  * 只修改订单状态为已发货
    //  */
    // async deliveyGoConfirm() {
    //   this.axios.post("order/goDelivery", {
    //     order_id: this.order_id,
    //   }).then((response) => {
    //     if (response.data.errno === 0) {
    //       this.getList();
    //       this.$message({
    //         type: "success",
    //         message: "发货成功!",
    //       });
    //     }else{
    //       this.$message({
    //         type: "error",
    //         message: "发货失败!",
    //       });
    //     }
    //   })
    // },
    // 创建订单快递单
    createdOrderExpressResp() {
      if (this.expressCode == "") {
        this.$message({
          type: "error",
          message: "请选择一个快递免单模板!",
        });
        return false;
      }
      this.sender.senderOptions = this.senderOptions;
      this.receiver.receiveOptions = this.receiveOptions;
      // 弹框提示是否打印
      this.$confirm("信息确认无误，是否继续打印快递单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        console.log(">>>this.orderInfo>>>", this.orderInfo);
        let printCargo = "";
        if (this.orderInfo.goodsList) {
          for (var item of this.orderInfo.goodsList) {
            printCargo += item.goods_aka + "*" + item.number + " ";
          }
        }
        // 长度超过20 则截取
        if (printCargo.length > 20) {
          printCargo = printCargo.substring(0, 18);
        }
        this.axios
          .post("order/createdOrderExpress", {
            printInfo: {
              printInfo: this.orderInfo.print_info,
              printCargo: printCargo || "唯优众宠订单",
            },
            sender: this.sender,
            receiver: this.receiver,
            expressType: this.expressType,
            expressCode: this.expressCode,
            partnerId: this.partnerId,
            orderSn: this.orderInfo.order_sn,
          })
          .then((response) => {
            if (response.data.errno === 0) {
              // 操作成功提示
              this.$message({
                type: "success",
                message: "打单成功",
              });
            }
          }).finally(() => {
            this.dialogFormVisible = false;
          });
      });
    },
    // 补打快递单
    supplementExpressResp() {
      // 弹框提示是否打印
      this.$confirm("补打快递单只能打印48小时内的面单，是否继续补打?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.axios
          .post("order/supplementExpress", {
            orderId: this.orderInfo.id,
          })
          .then((response) => {
            if (response.data.errno === 0) {
              // 操作成功提示
              this.$message({
                type: "success",
                message: "补打成功",
              });
            }
          }).finally(() => {
            this.dialogFormVisible = false;
          });
      });
    },

    // 点击发货按钮
    async checkExpressInfo() {
      await this.getOrderInfo(this.order_id);

      this.dialogFormVisible = true;
      this.dform = {
        method: 1,
      };
    },

    /**
     * 处理退款逻辑
     * @param agree 是否同意退款
     */
    handleRefundOrder(order, agree) {
      let mesg = agree ? '确定同意退款吗？' : '确定拒绝退款吗';
      this.$confirm(`${mesg}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (agree) {
          this.cancelRefundResp(order);
        } else {
          this.rejectRefundResp(order);
        }
      });
    },

    cancelRefundResp(order) {
      this.axios.post('pay/agentRefund', {
        orderSn: order.order_sn,
      }).then((response) => {
        if (response.data.errno === 0) {
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          this.getList();
        }
      });
    },
    rejectRefundResp(order) {
      this.axios.post('pay/rejectRefund', {
        orderSn: order.order_sn,
      }).then((response) => {
        if (response.data.errno === 0) {
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          this.getList();
        }
      });
    },

    /**
     * 处理退款逻辑
     * @param agree 是否同意退款
     */
    refundOrder(order) {
      this.$confirm(`确定已收货并完成退款吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.axios.post('pay/cancelRefund', {
          orderSn: order.order_sn,
        }).then((response) => {
          if (response.data.errno === 0) {
            this.$message({
              type: 'success',
              message: '操作成功!',
            });
            this.getList();
          }
        });
      });
    },

    handlePartialRefundOrder(order) {
      // 注释掉原来的逻辑，使用新的 RefundDialog
      /*
      this.$prompt('请输入退款金额，必须小于等于实付金额', '部分退款将直接退款', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^-?\d{1,3}(,\d{3})*(\.\d{1,2})?$|^-?\d+(\.\d{1,2})?$|^-?\.\d{1,2}$/,
        inputErrorMessage: '请输入正确金额'
      }).then(({ value }) => {
        if (parseFloat(value) > parseFloat(order.actual_price)) {
          this.$message({
            type: 'error',
            message: '退款金额必须小于等于实付金额!',
          });
          return false;
        }
        this.axios.post('pay/partialRefund', {
          orderSn: order.order_sn,
          refundAmount: value,
        }).then((response) => {
          if (response.data.errno === 0) {
            this.$message({
              type: 'success',
              message: '操作成功!',
            });
            this.getList();
          }
        });
      });
      */

      // 使用新的 RefundDialog
      this.refundMode = 'partial';
      this.currentRefundOrder = order;
      this.refundDialogVisible = true;
    },
    
    priceChangeConfirm() {
      if (
        this.orderInfo.actual_price == "" ||
        this.orderInfo.actual_price == 0
      ) {
        this.$message({
          type: "error",
          message: "总价不能为空!",
        });
        return false;
      }
      this.axios
        .post("order/orderPrice", {
            orderId: this.order_id,
            freightPrice: this.orderInfo.freight_price,
            goodsDiscountPrice: this.orderInfo.goods_discount_price
          })
        .then((response) => {
          this.dialogPriceVisible = false;
          this.getList();
        });
    },
    async getOrderInfo(sn) {
      this.axios
        .get("order/detail", {
          params: {
            orderId: this.order_id,
          },
        })
        .then((response) => {
          this.orderInfo = response.data.data.orderInfo;
          this.receiver = response.data.data.receiver;
          this.sender = response.data.data.sender;
          console.log(this.sender);
          this.receiveOptions = [];
          this.receiveOptions.push(
            this.receiver.province_id,
            this.receiver.city_id,
            this.receiver.district_id
          );
          this.senderOptions = [];
          this.senderOptions.push(
            this.sender.province_id,
            this.sender.city_id,
            this.sender.district_id
          );
        });
    },
    deliveryMethodChange(val) {
      if (val != 1) {
        this.expressType = "";
      }
    },

    handleSenderProvinceChange(value) {
        this.senderOptions = value;

        this.sender.province_id = this.senderOptions[0];
        this.sender.city_id = this.senderOptions[1];
        this.sender.district_id = this.senderOptions[2];

        // 将省市区id转成name 
        for (let item of this.options) {
            if (item.value == this.sender.province_id) {
                this.sender.province_name = item.label;
                for (let item1 of item.children) {
                    if (item1.value == this.sender.city_id) {
                        this.sender.city_name = item1.label;
                        for (let item2 of item1.children) {
                            if (item2.value == this.sender.district_id) {
                                this.sender.district_name = item2.label;
                            }
                        }
                    }
                }
            }
        }
    },
    handleReceiverProvinceChange(value) {
        this.senderOptions = value;

        this.receiver.province_id = this.senderOptions[0];
        this.receiver.city_id = this.senderOptions[1];
        this.receiver.district_id = this.senderOptions[2];

        // 将省市区id转成name 
        for (let item of this.options) {
            if (item.value == this.receiver.province_id) {
                this.receiver.province_name = item.label;
                for (let item1 of item.children) {
                    if (item1.value == this.receiver.city_id) {
                        this.receiver.city_name = item1.label;
                        for (let item2 of item1.children) {
                            if (item2.value == this.receiver.district_id) {
                                this.receiver.district_name = item2.label;
                            }
                        }
                    }
                }
            }
        }
    },

    changeExpressCode(code) {
        let expObj = this.shipperList.filter((item) => {
            return item.code == code;
        });
        if (expObj.length > 0) {
          this.expressType = expObj[0].temp_no;
          this.partnerId = expObj[0].month_code;
          this.expressCode = code;
        }
    },

    handleBatchCheckedChange() {
      console.log(this.batchList);
    },
    batchDelivery() {
      this.batchOrderList = [];
      if (this.batchList.length == 0) {
        this.$message({
          type: "error",
          message: "请选择订单!",
        });
        return false;
      }
      this.axios.post("order/detailBatch", {
        orderIds: this.batchList
      }).then((response) => {
        if (response.data.errno === 0) {
          if (response.data.data.length > 0) {
            this.batchOrderList = [...response.data.data];
          }
          
          this.showBatchDelivery = true;
        }else{
          this.$message({
            type: "error",
            message: "发货失败!",
          });
        }
      })

      // 获取setting
      this.axios.get('shipper').then((response) => {
          if (response.data.errno === 0) {
            this.shipperList = response.data.data.info;
          }
      })
    },
    changeBatchInfo(index, value) {
      this.batchOrderList[index].print_info = value;
    },
    async batchDeliveryConfirm() {
      if (!this.expressCode) {
        this.$message.error("请选择一个快递免单模板!");
        return;
      }

      try {
        for (const item of this.batchOrderList) {
          await this.batchCreatedOrderExpressResp(item);
        }

        this.$message.success("发货成功!");
      } catch (error) {
        const failedOrder = error?.orderNo || '部分订单';
        this.$message.error(`${failedOrder} 发货失败，请重试！`);
      } finally {
        this.showBatchDelivery = false;
        this.batchOrderList = [];
        this.batchList = [];
        this.getList();
      }
    },

    // 创建订单快递单
    async batchCreatedOrderExpressResp(orderItem) {
      try {
        const orderInfo = orderItem.orderInfo;
        let printCargo = "";
        if (orderInfo.goodsList) {
          for (var item of orderInfo.goodsList) {
            printCargo += item.goods_aka + "*" + item.number + " ";
          }
        }
        // 长度超过20 则截取
        if (printCargo.length > 20) {
          printCargo = printCargo.substring(0, 18);
        }
        await this.axios.post("order/createdOrderExpress", {
          printInfo: {
            printInfo: orderInfo.print_info,
            printCargo: printCargo || "唯优众宠订单",
          },
          sender: orderItem.sender,
          receiver: orderItem.receiver,
          expressType: this.expressType,
          expressCode: this.expressCode,
          partnerId: this.partnerId,
          orderSn: orderInfo.order_sn,
        });
      } catch (error) {
        error.orderNo = orderInfo.order_sn;
        throw error;
      }
    },

    // 强制退款
    handleForceRefundOrder(order) {
      // 注释掉原来的逻辑，使用新的 RefundDialog
      /*
      this.$confirm(`确定退款吗？该操作无法撤回，订单金额会原路返回到用户账户中`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.axios.post('pay/forceRefund', {
          orderSn: order.order_sn,
        }).then((response) => {
          if (response.data.errno === 0) {
            this.$message({
              type: 'success',
              message: '操作成功!',
            });
            this.getList();
          }
        });
      });
      */

      // 使用新的 RefundDialog
      this.refundMode = 'force';
      this.currentRefundOrder = order;
      this.refundDialogVisible = true;
    },

    showOrderExpress(item) {
      this.dialogOrderExpressVisible = true;
      this.order_id = item.id;
      this.orderExpressList = [];

      this.axios.get('order/getOrderExpressList', {
        params: {
          orderId: item.id,
        }
      }).then((response) => {
        if (response.data.errno === 0 && response.data.data.length > 0) {

          this.orderExpressList = response.data.data.map(item => {
            return {
              ...item,
              isEdit: false,
            }
          });
        }
      });
    },

    saveEditOrderExpress(item) {
      if (item.isEdit) {
        if (!item.shipper_code || !item.logistic_code) {
          this.$message({
            type: 'error',
            message: '请选择快递公司!',
          });
          return;
        }
        this.axios.post('order/saveOrderExpress', {
          orderId: this.order_id,
          shipperCode: item.shipper_code,
          logisticCode: item.logistic_code,
          expressId: item.id,
        }).then((response) => {
          if (response.data.errno === 0) {
            this.$message({
              type: 'success',
              message: '保存成功!',
            });
            item.isEdit = false;
          }
        });
      }else{
        item.isEdit = true;
      }
    },

    addEditOrderExpress() {
      this.orderExpressList.push({
        shipper_code: '',
        logistic_code: '',
        isEdit: true,
      });
    },

    deleteOrderExpress(item) {
      // 确定弹窗
      this.$confirm('确定要删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.axios.post('order/deleteOrderExpress', {
          id: item.id,
        }).then((response) => {
          if (response.data.errno === 0) {
            this.$message({
              type: 'success',
              message: '删除成功!',
            });
            this.orderExpressList = this.orderExpressList.filter((i) => {
              return i !== item;
            });
          }else{
            this.$message({
              type: 'error',
              message: response.data.errmsg || '删除失败!',
            });
          }
        });
      })
    },

    // 退款对话框确认事件
    onRefundConfirm(refundData) {
      const { mode, orderInfo, refundReason, refundAmount } = refundData;

      if (mode === 'force') {
        // 强制退款 API 调用
        const requestData = {
          orderSn: orderInfo.order_sn,
        };

        // 如果有退款原因，添加到请求参数中
        if (refundReason) {
          requestData.refundReason = refundReason;
        }

        this.axios.post('pay/forceRefund', requestData).then((response) => {
          if (response.data.errno === 0) {
            this.$message({
              type: 'success',
              message: '操作成功!',
            });
            this.getList();
          }
          this.refundDialogVisible = false;
        }).catch(() => {
          this.refundDialogVisible = false;
        });

      } else if (mode === 'partial') {
        // 部分退款 API 调用
        const requestData = {
          orderSn: orderInfo.order_sn,
          refundAmount: refundAmount,
        };

        // 如果有退款原因，添加到请求参数中
        if (refundReason) {
          requestData.refundReason = refundReason;
        }

        this.axios.post('pay/partialRefund', requestData).then((response) => {
          if (response.data.errno === 0) {
            this.$message({
              type: 'success',
              message: '操作成功!',
            });
            this.getList();
          }
          this.refundDialogVisible = false;
        }).catch(() => {
          this.refundDialogVisible = false;
        });
      }
    },

    // 退款对话框取消事件
    onRefundCancel() {
      this.refundDialogVisible = false;
      this.refundMode = '';
      this.currentRefundOrder = null;
    },
  },
  components: {
    ElButton,
    barcode: VueBarcode,
    RefundDialog,
  },

  mounted() {
    this.getList();
    this.getAllRegion();
  },
};
</script>

<style scoped>
.filter-input {
  width: 200px !important;
}

.float-right {
  float: right;
}

.d-btn {
  margin-bottom: 10px;
}

.print-footer {
  display: flex;
  justify-content: flex-end;
}

.print-footer .f-right {
  display: flex;
  justify-content: flex-end;
}

.btn-beihuo {
  margin-bottom: 22px;
}

.btn-fahuo {
  margin-bottom: 22px;
  margin-left: 30px;
}

.box-check {
  position: absolute;
  right: 0px;
  top: 4px;
}

.filter-box {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.filter-box .box {
  margin-right: 20px;
  margin-bottom: 10px;
}

.demo-form-inline {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  position: relative;
  flex-wrap: wrap;
}

.list-wrap {
  width: 100%;
  border: 1px solid #dfe5ed;
  margin-bottom: 10px;
}

.goods-img {
  width: 40px;
  height: 40px;
}

.list-wrap .header {
  width: 100%;
  height: 40px;
  background-color: rgba(236, 245, 255, 0.51);
  line-height: 40px;
  color: #1f2d3d;
  font-size: 13px;
  padding: 0 10px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.header .left {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.header .right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.off-text {
  color: #fff;
  border-radius: 4px;
  background: #594d72;
  line-height: 15px;
  padding: 4px 10px;
  font-size: 12px;
  margin-right: 10px;
}

.status-text {
  color: #f0797f;
  margin-right: 10px;
}

.add-time {
  margin-right: 20px;
}

.pay-time {
  margin-right: 20px;
}

.goods-num {
  margin-right: 20px;
}

.price-wrap {
  float: right;
  margin-right: 20px;
}

.edit-wrap {
  float: right;
  margin-top: 5px;
}

.price-change {
  float: right;
  margin-right: 10px;
  color: #e64242;
}

.content-wrap {
  width: 100%;
  display: flex;
  justify-content: flex-start;
}

.content-wrap .left {
  width: 30%;
  border-right: 1px solid #d1dbe5;
  padding: 20px 10px;
}

.content-wrap .user-wrap {
  width: 16%;
  border-right: 1px solid #d1dbe5;
  display: flex;
  flex-direction: column;
  padding: 20px 10px;
}

.content-wrap .user-wrap .avatar-wrap {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 10px;
}

.content-wrap .user-wrap .avatar-wrap .avatar-img {
  width: 40px;
  height: 40px;
  border-radius: 100px;
  margin-right: 10px;
}

.content-wrap .user-wrap .avatar-wrap .nickname {
  font-size: 14px;
}

.content-wrap .user-wrap .name {
  width: 100%;
  font-size: 14px;
}

.content-wrap .user-wrap .mobile {
  width: 100%;
  font-size: 14px;
}

.content-wrap .main {
  width: 36%;
  border-right: 1px solid #d1dbe5;
  padding: 20px 10px;
}

.content-wrap .right {
  width: 15%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.right .right-detail {
  margin-left: 0;
  margin-top: 6px;
}

.goods-list {
  display: flex;
  justify-content: flex-start;
  border-bottom: 1px solid #f1f1f1;
  padding: 6px 0;
  align-items: center;
}

.goods-list:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.goods-list:first-child {
  padding-top: 0;
}

.dialog-wrap .list-wrap {
  margin-bottom: 10px;
  padding: 10px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.dialog-wrap .goods-list {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  /*margin-bottom:20px;*/
  /*border-bottom:1px solid #d1dbe5;*/
}

.dialog-wrap .main {
  padding: 10px;
  margin-bottom: 20px;
  border: 1px solid #d1dbe5;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.dialog-wrap .main div {
  font-size: 14px;
}

.goods-name {
  color: #5e7382;
  font-size: 14px;
  margin: 0 20px 0 10px;
  width: 180px;
}

.goods-spec {
  color: #0066cc;
  font-size: 14px;
  margin-right: 30px;
  width: 60px;
}

.goods-number {
  color: #ff3456;
  font-size: 14px;
  margin-right: 20px;
}

.goods-number label {
  color: #666;
}

.goods-price {
  color: #333;
  font-size: 14px;
  margin-right: 20px;
}

.m1 {
  display: flex;
  justify-content: flex-start;
}

.dialog-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /*background: #f6fdff;*/
  border-bottom: 1px solid #f1f1f1;
}

.dialog-main .l {
  display: flex;
  justify-content: flex-start;
}

.other {
  /*background: #f1f1f1;*/
  border-top: none;
}

.dialog-main .title {
  /*background: #ecf0ff;*/
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20px;
}

.other .title {
  background: #eaeaea;
}

.user-name {
  color: #000000;
  font-size: 14px;
  margin-right: 10px;
  line-height: 20px;
}

.user-mobile {
  color: #000000;
  font-size: 14px;
  line-height: 20px;
  margin-right: 20px;
}

.user-address {
  color: #333;
  font-size: 13px;
  line-height: 20px;
  margin-top: 10px;
  display: flex;
  justify-content: flex-start;
}

.user-post {
  color: #333;
  font-size: 14px;
  line-height: 20px;
  margin-top: 4px;
  background-color: #fbf7c5;
  padding: 10px;
}

.detail {
  padding: 10px 0;
}

.receive-detail {
  padding: 10px 0;
}

.user-post-t {
  color: #333;
  font-size: 14px;
  line-height: 20px;
  margin-top: 4px;
  background-color: #fbf7c5;
  padding: 10px;
  margin: 10px 0;
}

.user-admin-t {
  color: #333;
  font-size: 14px;
  line-height: 20px;
  margin-top: 4px;
  background-color: #fde7e7;
  padding: 10px;
  margin: 10px 0;
}

.admin-memo {
  margin-top: 10px;
}

.el-input {
  width: 300px;
}

.address-input {
  margin-left: 10px;
}

.senderInput {
  width: 200px !important;
}

.senderInput .el-input__inner {
  width: 100px;
}

.senderAddressInput {
  width: 530px !important;
  margin-bottom: 10px;
}

.el-checkbox {
  margin-bottom: 22px;
  margin-right: 20px;
}

.express-info {
  padding: 10px;
  color: #ff3456;
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 10px;
  background: #f0f0f0;
}

.el-form-item {
  margin-bottom: 10px;
}
.flex-row {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.flex-col {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
/*.express-dialog{*/
/*display: flex;*/
/*!*justify-content: center;*!*/
/*}*/
</style>
