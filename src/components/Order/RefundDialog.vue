<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="500px"
    :before-close="handleCancel"
    @close="handleCancel"
  >
    <el-form
      ref="refundForm"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <!-- 部分退款时显示金额输入框 -->
      <el-form-item
        v-if="mode === 'partial'"
        label="退款金额"
        prop="refundAmount"
      >
        <el-input
          v-model="form.refundAmount"
          placeholder="请输入退款金额"
          type="number"
          step="0.01"
        >
          <template slot="append">元</template>
        </el-input>
        <div class="form-tip">
          实付金额：{{ orderInfo.actual_price || 0 }} 元
        </div>
      </el-form-item>

      <!-- 退款原因输入框（可选） -->
      <el-form-item
        label="退款原因"
        prop="refundReason"
      >
        <el-input
          v-model="form.refundReason"
          type="textarea"
          :rows="3"
          placeholder="请输入退款原因"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        type="primary"
        @click="handleConfirm"
        :loading="loading"
      >
        确认{{ mode === 'force' ? '强制退款' : '部分退款' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'RefundDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'force', // 'force' 或 'partial'
      validator: value => ['force', 'partial'].includes(value)
    },
    orderInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      form: {
        refundAmount: '',
        refundReason: ''
      },
      rules: {
        refundAmount: [
          {
            required: true,
            message: '请输入退款金额',
            trigger: 'blur'
          },
          {
            validator: this.validateRefundAmount,
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    dialogTitle() {
      return this.mode === 'force' ? '强制退款' : '部分退款'
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.resetForm()
      }
    }
  },
  methods: {
    // 验证退款金额
    validateRefundAmount(rule, value, callback) {
      if (this.mode !== 'partial') {
        callback()
        return
      }

      const amount = parseFloat(value)
      const actualPrice = parseFloat(this.orderInfo.actual_price || 0)

      if (isNaN(amount) || amount <= 0) {
        callback(new Error('退款金额必须大于0'))
        return
      }

      if (amount > actualPrice) {
        callback(new Error('退款金额不能超过实付金额'))
        return
      }

      callback()
    },

    // 确认退款
    handleConfirm() {
      // 如果是强制退款，不需要验证表单
      if (this.mode === 'force') {
        this.submitRefund()
        return
      }

      // 部分退款需要验证表单
      this.$refs.refundForm.validate((valid) => {
        if (valid) {
          this.submitRefund()
        }
      })
    },

    // 提交退款
    submitRefund() {
      const refundData = {
        mode: this.mode,
        orderInfo: this.orderInfo,
        refundReason: this.form.refundReason.trim()
      }

      if (this.mode === 'partial') {
        refundData.refundAmount = this.form.refundAmount
      }

      this.$emit('confirm', refundData)
    },

    // 取消操作
    handleCancel() {
      this.$emit('cancel')
      this.resetForm()
    },

    // 重置表单
    resetForm() {
      this.form = {
        refundAmount: '',
        refundReason: ''
      }
      this.loading = false
      
      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.refundForm) {
          this.$refs.refundForm.clearValidate()
        }
      })
    },

    // 设置加载状态
    setLoading(loading) {
      this.loading = loading
    }
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}
</style>
