<template>
    <div class="content-page">
        <div class="content-nav">
            <el-breadcrumb class="breadcrumb" separator="/">
                <el-breadcrumb-item>商品品牌</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="content-main">
            <div class="form-table-box">
                <div class="btn-wrap">
                    <el-button @click="addBrand" plain type="primary" icon="plus">添加品牌</el-button>
                </div>
                <el-table :data="brandData" style="width: 100%; height: 800px;" border stripe>
                    <el-table-column prop="id" label="ID" width="100">
                    </el-table-column>
                    <el-table-column prop="brand_name" label="品牌名称">
                    </el-table-column>
                    <el-table-column prop="create_time" label="添加时间" width="200">
                    </el-table-column>
                    <el-table-column label="操作" width="360">
                        <template slot-scope="scope">
                            <el-button size="small" @click="addBrand(scope.row)">编辑</el-button>
                            <el-button size="small" @click="goodsBrandListAction(scope.row)">商品明细</el-button>
                            <el-button size="small" type="danger" @click="deleteAction(scope.row)">删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <div class="page-box">
                <el-pagination @current-change="brandListHandlePageChange" :current-page="brandPage" :page-size="brandSize"
                               layout="total, prev, pager, next, jumper" :total="brandTotal">
                </el-pagination>
            </div>
        </div>

        <el-dialog :visible.sync="dialogVisible" size="mini">
            <el-form ref="infoForm" :rules="infoRules" :model="infoForm" label-width="120px">
                <el-form-item label="品牌名称" prop="brand_name">
                    <el-input v-model="infoForm.brand_name"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="onSubmitInfo">确定保存</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>

        <el-dialog :visible.sync="goodsBrandListDialogVisible">
            <!-- 新增商品 -->
            <div class="btn-wrap">
                <div class="flex-row" label="商品名称" prop="name">
                    <el-input v-model="goodsName" placeholder="请输入商品名称" style="width: 200px; margin-right: 20px;"></el-input>
                    <el-button @click="getGoodsBrandList" plain type="primary" icon="plus">搜索</el-button>
                </div>
                <el-button @click="addGoodsToBrand" plain type="primary" icon="plus">新增商品</el-button>
            </div>
            <el-table :data="goodsBrandList" style="width: 100%; height: 700px;" border stripe>
                <el-table-column prop="id" label="ID" width="100">
                </el-table-column>
                <el-table-column prop="list_pic_url" label="商品图片" width="80">
                    <template slot-scope="scope">
                        <img :src="scope.row.list_pic_url" alt="" style="width: 40px;height: 40px">
                    </template>
                </el-table-column>
                <el-table-column prop="name" label="商品名称">
                </el-table-column>
                <el-table-column prop="brand_name" label="商品品牌">
                </el-table-column>
                <!-- 删除 -->
                 <el-table-column label="操作" width="160">
                    <template slot-scope="scope">
                        <el-button size="small" type="danger" @click="deleteGoodsFromBrand(scope.row)">删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
             <div class="page-box">
                <el-pagination @current-change="goodsBrandListHandlePageChange" :current-page="goodsBrandPage" :page-size="goodsBrandSize"
                               layout="total, prev, pager, next, jumper" :total="goodsBrandTotal">
                </el-pagination>
            </div>
        </el-dialog>

        <el-dialog :visible.sync="goodsListDialogVisible">
            <!-- 保存按钮 -->
             <div class="btn-wrap">
                <!-- 商品名称搜索 -->
                <div class="flex-row" label="商品名称" prop="name">
                    <el-input v-model="goodsName" placeholder="请输入商品名称" style="width: 200px; margin-right: 20px;"></el-input>
                    <el-button @click="getGoodsList" plain type="primary" icon="plus">搜索</el-button>
                </div>
                <el-button @click="saveGoodsToBrand" plain type="primary" icon="plus">保存</el-button>
            </div>
            <el-table 
                :data="goodsList" 
                style="width: 100%; height: 700px;" border 
                stripe
                @selection-change="handleSelectionChange"
            >
                <el-table-column type="selection" width="55"></el-table-column>
                <el-table-column prop="id" label="ID" width="100">
                </el-table-column>
                <el-table-column prop="list_pic_url" label="商品图片" width="80">
                    <template slot-scope="scope">
                        <img :src="scope.row.list_pic_url" alt="" style="width: 40px;height: 40px">
                    </template>
                </el-table-column>
                <el-table-column prop="name" label="商品名称">
                </el-table-column>
            </el-table>
            <!-- 分页 -->
             <div class="page-box">
                <el-pagination @current-change="goodsListHandlePageChange" :current-page="goodsPage" :page-size="goodsSize"
                               layout="total, prev, pager, next, jumper" :total="goodsTotal">
                </el-pagination>
            </div>
        </el-dialog>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                dialogFormVisible:false,
                brandData: [],
                infoForm: {
                    id: 0,
                    brand_name: ''
                },
                dialogVisible: false,
                infoRules: {
                    brand_name: [
                        {required: true, message: '请输入名称', trigger: 'blur'},
                    ],
                },

                brandPage: 1,
                brandSize: 10,
                brandTotal: 0,

                multipleSelection: [],

                goodsListDialogVisible: false,
                goodsList: [],
                goodsPage: 1,
                goodsSize: 8,
                goodsTotal: 0,
                goodsName: "",

                goodsBrandListDialogVisible: false,
                goodsBrandList: [],
                goodsBrandPage: 1,
                goodsBrandSize: 8,
                goodsBrandTotal: 0,
            }
        },
        methods: {
            addBrand(row) {
                this.dialogVisible = true;
                if (row) {
                    this.infoForm.id = row.id;
                    this.infoForm.brand_name = row.brand_name;
                }
            },
            onSubmitInfo() {
                this.$refs['infoForm'].validate((valid) => {
                    if (valid) {
                        this.axios.post('goods/editGoodsBrand', this.infoForm).then((response) => {
                            if (response.data.errno === 0) {
                                this.$message({
                                    type: 'success',
                                    message: '保存成功!'
                                });
                                this.dialogVisible = false;
                                this.getList();
                            }
                        })
                    } else {
                        return false;
                    }
                });
            },
            deleteAction(row) {
                this.$confirm('确定要删除?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.axios.post('goods/deleteGoodsBrand', {id: row.id}).then((response) => {
                        console.log(response.data)
                        if (response.data.errno === 0) {
                            this.$message({
                                type: 'success',
                                message: '删除成功!'
                            });

                            this.getList();
                        }
                    })
                });
            },
            brandListHandlePageChange(val) {
                this.brandPage = val;
                this.getList();
            },
            getList() {
                this.axios.get('goods/getGoodsBrand', {
                    params: {
                        page: this.brandPage,
                        size: this.brandSize,
                    }
                }).then((response) => {
                    if (response.data.errno === 0) {
                        this.brandData = response.data.data.data;
                        this.brandTotal = response.data.data.count;
                    }
                })
            },

            goodsBrandListAction(row) {
                if (row) {
                    this.infoForm.id = row.id;
                    this.infoForm.brand_name = row.brand_name;
                    this.getGoodsBrandList();
                }
            },
            getGoodsBrandList() {
                this.axios.get('goods/getGoodsListByBrandId', {
                    params: {
                        page: this.goodsPage,
                        brand_id: this.infoForm.id,
                        name: this.goodsName,
                        page: this.goodsBrandPage,
                        size: this.goodsBrandSize,
                    }
                }).then((response) => {
                    if (response.data.errno === 0) {
                        this.goodsBrandList = response.data.data.data;
                        this.goodsBrandTotal = response.data.data.count;
                    }
                    this.goodsBrandListDialogVisible = true;
                })
            },
            goodsBrandListHandlePageChange(val) {
                this.goodsBrandPage = val;
                this.getGoodsBrandList();
            },

            handleSelectionChange(val) {
                this.multipleSelection = val;
            },

            addGoodsToBrand() {
                this.getGoodsList();
            },
            getGoodsList() {
                this.axios.get('goods/getGoodsListNotInBrand', {
                    params: {
                        page: this.goodsPage,
                        size: this.goodsSize,
                        name: this.goodsName
                    }
                }).then((response) => {
                    if (response.data.errno === 0) {
                        this.goodsList = response.data.data.data;
                        this.goodsTotal = response.data.data.count;
                    }

                    this.goodsListDialogVisible = true;
                })
            },
            goodsListHandlePageChange(val) {
                this.goodsPage = val;
                this.getGoodsList();
            },

            saveGoodsToBrand() {
                if (this.multipleSelection.length == 0) {
                    this.$message({
                        type: 'error',
                        message: '请选择商品'
                    })
                    return false;
                }
                let goods_ids = [];
                this.multipleSelection.forEach((item) => {
                    goods_ids.push(item.id);
                })
                this.axios.post('goods/addGoodsToBrand', {
                    goods_ids: goods_ids,
                    brand_id: this.infoForm.id
                }).then((response) => {
                    if (response.data.errno === 0) {
                        this.$message({
                            type: 'success',
                            message: '保存成功'
                        })
                        this.goodsListDialogVisible = false;
                        this.goodsName = "";
                        this.getGoodsBrandList();
                    }
                })
            },

            deleteGoodsFromBrand(row) {
                this.$confirm('确定要删除?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.axios.post('goods/deleteGoodsFromBrand', {
                        goods_id: row.id,
                        // brand_id: this.infoForm.id
                    }).then((response) => {
                        if (response.data.errno === 0) {
                            this.$message({
                                type: 'success',
                                message: '删除成功'
                            })
                            this.getGoodsBrandList();
                        }
                    })
                });
            }
        },
        components: {},
        mounted() {
            this.getList();
        }
    }

</script>

<style scoped>
    .sort-width{
        width: 90px;
    }
    .right{
        float: right;
    }
    .form-inline {
        margin-top: 2px;
        height: 40px;
        margin-right: 20px;
    }
    .block {
        margin-bottom: 10px;
        height:42px;
        display: flex;
        align-items: center;
        justify-content:space-between;
    }
    .active {
        border-color: #ff4949;
        color: #ff4949;
    }
    .marginRight{
        margin-right: 20px;
    }
    .btn-wrap{
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }
</style>
