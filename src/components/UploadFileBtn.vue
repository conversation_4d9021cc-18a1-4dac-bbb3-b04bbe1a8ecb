<template>
	<div class="flex-row" style="position: relative; width: 100%; height: 100%;">
		<template v-if="imgUrl">
			<video
				v-if="showVideo"
				:src="imgUrl"
				controls
				style="width: 100%; height: 100%;"
			></video>
			<el-image
				v-else
				fit="cover"
				style="width: 100%; height: 100%;"
				:src="imgUrl"
				:preview-src-list="[imgUrl]">
			</el-image>
		</template>
		<div v-else style="position: relative; width: 100%; height: 100%;">
				<el-image
					:src="btnImageUrl"
					style="width: 100%; height: 100%;"
					fit="contain"
					>
				</el-image>
				<input
					id="file"
					class="add-view"
					style="position: absolute; width: 100%; height: 100%; opacity: 0; top: 0; left: 0"
					type="file"
					@change='uploadFile($event)'
					:accept="acceptList"
					:multiple="multiple"
				/>
		</div>
		<div v-if="imgUrl" class="o-shadow" @click="deleteImageAction">
			<i class="el-icon-delete" style="color: rgba(0, 0, 0, .5);"></i>
		</div>

	</div>
</template>

<script>
import COS from 'cos-js-sdk-v5';

var Bucket ='petshop-1345322801';          // 腾讯云对象储存桶名
var Region = 'ap-nanjing';                    // 对象储存你所处的地区编号
var Thum = '?imageView2/1/w/100/h/100/q/85!';  // 缩略图后缀
var MaxMSize = 5;                              // 图片最大M数
var VideoMaxMSize = 30;
var cos = new COS({
	SecretId: 'AKIDJSM0OWOzLRGp8B0cy44zf7KLAoMp8B0b',  // 密钥ID
	SecretKey: 'QZS5SZXFhtpVv5zf6JHzRdZFfpiLipgN',     // 密钥的钥匙
});

export default {
	props: {
		imgUrl: {
			type: String,
			default: ''
		},
		multiple: {
			type: Boolean,
			default: false
		},
		// 是否支持视频
		video: {
			type: Boolean,
			default: false
		},
		accept: {
			type: String,
			default: 'image/gif, image/jpg, image/jpeg, image/png, image/bmp'
		}
	},
	watch: {
		imgUrl: function (val) {
			this.imgFileUrl = val;
			// 当URL改变时，尝试检测文件类型
			this.detectFileTypeFromUrl(val);
		}
	},
	computed: {
		showVideo() {
			return this.imgUrl && this.fileType.indexOf('video') > -1;
		}
	},
	data(){
		return{
			loading: false,
			btnImageUrl: require("@/assets/btn_add_img.png"),
			addImg: require('@/assets/addImg.png'),   // 新增图片地址
			fileUrl: require('@/assets/addImg.png'),  // 对象地址
			btnText: '上传图片',  // 按钮文字
			imgFileUrl: '',
			imgFileUrlList: [],
			acceptList: [],
			fileType: '',
			// type: Array,
			// default: function () {
			// 	return [
			// 		'image/gif',
			// 		'image/jpg',
			// 		'image/jpeg',
			// 		'image/png',
			// 		'image/bmp',
			// 		'video/mp4',
			// 		'video/avi',
			// 		'video/wmv',
			// 		'video/mov',
			// 		'video/flv',
			// 	]
			// }

		}
	},
	methods: {
		// 上传文件
		uploadFile: function (e) {
			if (this.multiple) {
				// 多次上传文件
				this.loading = true;
				// 将FileList转换为数组
				const filesArray = Array.from(e.target.files);
				// 清空已有的URL列表
				this.imgFileUrlList = [];

				// 使用Promise.all处理多个文件上传
				Promise.all(filesArray.map(file => this.uploadFileFunc(file)))
					.then(results => {
						// 与父组件通信
						results.forEach(data => {
							if (data && data.Url) {
								this.imgFileUrlList.push(data.Url);
							}
						});

						// 发送所有文件URL到父组件
						this.$emit("getMultipleFileUrls", this.imgFileUrlList);
					})
					.catch(err => {
						this.$message({
							message: '上传文件失败: ' + (err.message || '未知错误'),
							type: 'error'
						});
					})
					.finally(() => {
						// 清空选中文件
						let f = document.getElementById('file');
						f.value = '';
						this.loading = false;
					});
			} else {
				// 单文件上传
				this.loading = true;
				this.uploadFileFunc(e.target.files[0])
					.then(data => {
						this.fileUrl = data.Url + Thum;
						// 保存文件类型和URL的关联
						this.imgFileUrl = data.Url;
						// 与父组件通信
						this.$emit("getFileUrl", {
							highimage: data.Url,
							thumimage: this.fileUrl,
							fileType: this.fileType // 将文件类型也传递给父组件
						});
					})
					.catch(err => {
						this.$message({
							message: '上传文件失败: ' + (err.message || '未知错误'),
							type: 'error'
						});
					})
					.finally(() => {
						// 清空选中文件
						let f = document.getElementById('file');
						f.value = '';
						this.loading = false;
					});
			}
		},
		uploadFileFunc: function (file) {
			// 如果没有文件，返回一个rejected的Promise
			if (!file) return Promise.reject(new Error('没有选择文件'));
			this.fileType = file.type;
			const timeStamp = (new Date()).valueOf();
			const copyFile = new File([file], `${timeStamp}_${file.name}`);
			const maxSize = (this.video ? VideoMaxMSize : MaxMSize);
			// 限制图片最大尺寸
			let isLimit = copyFile.size / 1024 / 1024 > maxSize;
			if(isLimit) {
				this.$message({
					message: '文件过大，请选择大小在 ' + maxSize + 'M 内的图片！',
					type: 'error'
				});
				return Promise.reject(new Error(`文件大小超过${maxSize}M限制`));
			}

			// 返回一个Promise对象
			return new Promise((resolve, reject) => {
				// 分片上传文件
				cos.putObject({
					Bucket: Bucket,
					Region: Region,
					Key: copyFile.name,
					Body: copyFile,
					onProgress: function (progressData) {
						if(progressData.percent === 1) {
							// 获取上传成功后的Url地址，通过这个地址查看、下载上传的文件
							cos.getObjectUrl({
								Bucket: Bucket,
								Region: Region,
								Key: copyFile.name,
								Protocol: 'https',
								Sign: false,
							}, function (err, data) {
								if (err) {
									reject(err);
								} else {
									resolve(data);
								}
							});
						}
					},
				}, function (err) {
					if (err) {
						reject(err);
					}
				});
			});
		},
		// 改变文件路径
		changeFileUrl: function (url, txt) {
			if(!url) {
				this.fileUrl = this.addImg;
			} else {
				this.fileUrl = url;
			}
			this.btnText = txt;
		},

		deleteImageAction() {
			this.$emit("deleteImage");
			// 清除文件类型
			this.fileType = '';
		},

		// 从URL尝试检测文件类型
		detectFileTypeFromUrl(url) {
			if (!url) {
				this.fileType = '';
				return;
			}

			// 从URL的扩展名尝试判断文件类型
			const extension = url.split('.').pop().toLowerCase();

			// 视频文件扩展名
			const videoExtensions = ['mp4', 'avi', 'wmv', 'mov', 'flv'];
			// 图片文件扩展名
			const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp'];

			if (videoExtensions.includes(extension)) {
				this.fileType = 'video/' + extension;
			} else if (imageExtensions.includes(extension)) {
				this.fileType = 'image/' + extension;
			} else {
				// 如果无法从扩展名判断，尝试从URL中的查询参数判断
				if (url.includes('video')) {
					this.fileType = 'video/unknown';
				} else if (url.includes('image')) {
					this.fileType = 'image/unknown';
				} else {
					this.fileType = '';
				}
			}
		}
	},
	// 创建VUE实例后的钩子
	created: function () {

	},
	// 挂载到DOM后的钩子
	mounted: function () {
		this.imgFileUrl = this.imgUrl;
		// 初始化时检测文件类型
		this.detectFileTypeFromUrl(this.imgUrl);

		// 设置接受的文件类型列表
		if (this.video) {
			this.acceptList.push(
				...['video/mp4',
				'video/avi',
				'video/wmv',
				'video/mov',
				'video/flv']
			);
		}
	}
}
</script>

<style lang="scss">
.add-view:hover {
	cursor: pointer;
}
.o-shadow {
	position: absolute;
	bottom: 0px;
	right: 0px;
	transition: opacity .3s;
	color: #fff;
	font-size: 20px;
	line-height: 20px;
	padding: 10px;
	cursor: pointer;
}
</style>