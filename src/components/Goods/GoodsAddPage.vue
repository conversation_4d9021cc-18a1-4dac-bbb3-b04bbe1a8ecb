<template>
  <div class="content-page">
    <div class="content-nav">
      <el-breadcrumb class="breadcrumb" separator="/">
        <el-breadcrumb-item :to="{ name: 'goods' }"
          >商品管理</el-breadcrumb-item
        >
        <el-breadcrumb-item>{{
          infoForm.id ? "编辑商品" : "添加商品"
        }}</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="operation-nav">
        <!-- <el-button type="primary" @click="test">测试</el-button> -->
        <el-button type="primary" @click="onSubmitInfo">确定保存</el-button>
        <el-button @click="goBackPage" icon="arrow-left">返回列表</el-button>
      </div>
    </div>
    <div class="content-main">
      <div class="form-table-box">
        <el-form
          ref="infoForm"
          :rules="infoRules"
          :model="infoForm"
          label-width="120px"
        >
          <el-form-item label="商品分类">
            <el-select
              class="el-select-class"
              v-model="cateId"
              placeholder="选择型号分类"
            >
              <el-option
                v-for="item in cateOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            label="商品图片"
            prop="list_pic_url"
          >
            <div class="index-image">
							<UploadFileBtn :imgUrl="infoForm.list_pic_url" @getFileUrl="handleUploadImageSuccess($event, 'list_pic_url')" @deleteImage="deleteImage('list_pic_url')" />
						</div>
          </el-form-item>
          <el-form-item label="商品轮播图" prop="goods_sn">
            <draggable
              v-model="gallery_list"
              draggable=".gallery-item"
              class="drag-wrap"
              style="display: flex; flex-direction: row;"
            >
              <div
                v-for="(element, index) in gallery_list"
                class="gallery-item"
                v-if="element.is_delete == 0"
              >
                <div style="width: 148px; height: 148px; margin: 0 10px 10px 0">
                  <UploadFileBtn
                    :imgUrl="element.url"
                    @getFileUrl="handleUploadBannerImageSuccess($event, index)"
                    @deleteImage="deleteBannerImage(index)"
                    :video="true"
                  />
                </div>
              </div>
              <div style="width: 148px; height: 148px; margin: 0 10px 10px 0">
                <UploadFileBtn @getFileUrl="handleUploadBannerImageSuccess($event, -1)" />
              </div>

            </draggable>
          </el-form-item>
          <el-form-item label="商品名称" prop="name">
            <el-input v-model="infoForm.name"></el-input>
          </el-form-item>
          <el-form-item label="商品品牌" prop="goods_brand">
            <!-- <el-input v-model="infoForm.goods_brand"></el-input> -->
             <el-select
              class="el-select-class"
              v-model="infoForm.brand_id"
              placeholder="选择商品品牌"
            >
              <el-option
                v-for="item in brandOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="商品简介" prop="goods_brief">
            <el-input
              type="textarea"
              v-model="infoForm.goods_brief"
              :rows="3"
            ></el-input>
            <div class="form-tip"></div>
          </el-form-item>
          <el-form-item label="商品单位" prop="goods_unit">
            <el-input v-model="infoForm.goods_unit"></el-input>
            <div class="form-tip">如：件、包、袋</div>
          </el-form-item>
          <el-form-item label="销量" prop="sell_volume">
            <el-input v-model="infoForm.sell_volume"></el-input>
          </el-form-item>
          <el-form-item label="型号和价格">
            <div class="spec-wrap">
              <el-table :data="specData" stripe style="width: 100%">
                <el-table-column prop="goods_sn" label="商品SKU" width="140">
                  <template slot-scope="scope">
                    <el-input
                      @blur="checkSkuOnly(scope.$index, scope.row)"
                      size="mini"
                      v-model="scope.row.goods_sn"
                      disabled
                      placeholder="商品SKU"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="goods_aka"
                  label="快递单上的简称"
                  width="260"
                >
                  <template slot-scope="scope">
                    <el-input
                      size="mini"
                      v-model="scope.row.goods_name"
                      placeholder="简称"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="goods_specifition_name" label="型号/规格" width="260">
                  <template slot-scope="scope">
                    <el-input
                      size="mini"
                      v-model="scope.row.goods_specifition_name"
                      placeholder="如1斤/条"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="market_price" label="市场价(元)" width="100">
                  <template slot-scope="scope">
                    <el-input
                      size="mini"
                      v-model="scope.row.market_price"
                      placeholder="市场价"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="retail_price"
                  label="零售(元)"
                  width="100"
                >
                  <template slot-scope="scope">
                    <el-input
                      size="mini"
                      v-model="scope.row.retail_price"
                      placeholder="零售"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="agent_price" label="代理商价格(元)" width="120">
                    <template slot-scope="scope">
                        <el-input size="mini" v-model="scope.row.agent_price"
                                  placeholder="代理商价格"></el-input>
                    </template>
                </el-table-column>
                <el-table-column
                  prop="goods_weight"
                  label="重量(g)"
                  width="100"
                >
                  <template slot-scope="scope">
                    <el-input
                      size="mini"
                      v-model="scope.row.goods_weight"
                      placeholder="重量"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="goods_number" label="库存" width="100">
                  <template slot-scope="scope">
                    <el-input
                      size="mini"
                      v-model="scope.row.goods_number"
                      placeholder="库存"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="70">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="danger"
                      icon="el-icon-delete"
                      circle
                      @click="specDelete(scope.$index, scope.row)"
                    >
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-button class="marginTop20" type="primary" @click="addSpecData"
                >新增型号</el-button
              >
            </div>
          </el-form-item>
          <el-form-item label="属性设置" class="checkbox-wrap">
            <el-checkbox-group v-model="infoForm.is_new" class="checkbox-list">
              <el-checkbox label="新品" name="is_new"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="选择快递模板">
            <el-select
              v-model="kdValue"
              placeholder="请选择快递"
              @change="kdChange"
              clearable
              class="el-select-class"
            >
              <el-option
                v-for="item in kdOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <div style="color: red;font-size: 14px;margin-top: 10px;">
              不设置专属模版将自动使用全局模版
            </div>
          </el-form-item>
          <el-form-item label="快递计费">
            <el-radio-group v-model="infoForm.freight_mode">
              <el-radio :label="0">使用模版</el-radio>
              <el-radio :label="1">一件包邮</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="排序" prop="sort_order">
            <el-input-number
              :mini="1"
              :max="100"
              v-model="infoForm.sort_order"
            ></el-input-number>
          </el-form-item>
          <el-form-item label=" ">
            <el-switch
              active-text="上架"
              inactive-text="下架"
              active-value="1"
              inactive-value="0"
              v-model="infoForm.is_on_sale"
            ></el-switch>
          </el-form-item>
          <el-form-item label="商品详情" prop="goods_desc">
            <div class="edit_container">
              <quill-editor
                v-model="infoForm.goods_desc"
                ref="myTextEditor"
                class="editer"
                :options="editorOption"
                @blur="onEditorBlur($event)"
                @ready="onEditorReady($event)"
              >
              </quill-editor>
            </div>
          </el-form-item>
          <!-- 图片上传组件辅助-->
          <el-form-item class="upload_ad">
            <UploadFileBtn
              class="avatar-uploader"
              @getMultipleFileUrls="handleUploadDetailImageSuccess"
              :multiple="true"
            />
          </el-form-item>
          <!-- 视频上传组件辅助-->
          <el-form-item class="upload_ad">
            <UploadFileBtn
              class="video-uploader"
              @getFileUrl="handleUploadDetailVideoSuccess"
              :video="true"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmitInfo">确定保存</el-button>
            <el-button @click="goBackPage">返回列表</el-button>
            <el-button
              type="danger"
              class="float-right"
              @click="onCopyGood"
              v-if="infoForm.id > 0"
              >复制商品</el-button
            >
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-dialog :visible.sync="dialogVisible" size="tiny">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
  </div>
</template>

<script>
import lrz from "lrz";
import moment from "moment";
import 'quill/dist/quill.core.css';
import 'quill/dist/quill.snow.css';
import 'quill/dist/quill.bubble.css';
import draggable from "vuedraggable";
import $ from "jquery";
import { quillEditor, Quill } from "vue-quill-editor";
import UploadFileBtn from '@/components/UploadFileBtn.vue';
import Video from '../../config/video';
Quill.register(Video, true);

const toolbarOptions = [
  ["bold", "italic", "underline", "strike"],
  ["blockquote"],
  [
    {
      list: "ordered",
    },
    {
      list: "bullet",
    },
  ],
  [
    {
      indent: "-1",
    },
    {
      indent: "+1",
    },
  ],
  [
    {
      size: ["small", false, "large", "huge"],
    },
  ],
  [
    {
      header: [1, 2, 3, 4, 5, 6, false],
    },
  ],
  // ["link", "image", "video"],
  ["image", "video"],
];
export default {
  data() {
    return {
      picData: {
        token: "",
      },
      url: "",
      kdOptions: [],
      kdValue: "",
      cateId: "",
      detail_list: [],
      dialogImageUrl: "",
      dialogVisible: false,
      options: [],
      cateOptions: [],
      editorOption: {
        modules: {
          toolbar: {
            container: toolbarOptions, // 工具栏
            handlers: {
              image: function (value) {
                if (value) {
                  document.querySelector(".avatar-uploader input").click();
                } else {
                  this.quill.format("image", false);
                }
              },
              video: function (value) {
                if (value) {
                  document.querySelector(".video-uploader input").click();
                } else {
                  this.quill.format("video", false);
                }
              },
            },
          },
          syntax: {
            highlight: (text) => hljs.highlightAuto(text).value,
          },
        }
      },
      category: [],
      infoForm: {
        name: "",
        list_pic_url: "",
        goods_brief: "",
        goods_brand: "",
        goods_desc: "",
        is_on_sale: 0,
        is_new: false,
        freight_mode: 0,
        // is_index: false,
      },
      brandOptions: [],
      infoRules: {
        name: [
          {
            required: true,
            message: "请输入名称",
            trigger: "blur",
          },
        ],
        goods_brief: [
          {
            required: true,
            message: "请输入简介",
            trigger: "blur",
          },
        ],
        list_pic_url: [
          {
            required: true,
            message: "请选择商品图片",
            trigger: "blur",
          },
        ],
      },
      specData: [
        // {
        //   goods_sn: "",
        //   goods_specifition_name: "",
        //   market_price: "",
        //   retail_price: "",
        //   agent_price: "",
        //   goods_weight: "",
        //   goods_number: "",
        // },
      ],
      selectedSpec: "规格",
      is_has_spec: false,
      gallery: {
        goods_id: 0,
      },
      gallery_list: [],
      visible: false,
      hasPost: 0,
      previewList: [],
      autoFocus: false,
    };
  },
  methods: {
    getGoodsBrand() {
      let that = this;
      this.axios
        .get("goods/getGoodsBrand", {
          params: {},
        })
        .then((response) => {
          if (response.data.errno === 0) {
            that.brandOptions = response.data.data.data.map(item => {
              return {
                value: item.id,
                label: item.brand_name,
              }
            });
          }
        });
    },
    checkSkuOnly(index, row) {
      console.log(index);
      console.log(row);
      if (row.goods_sn == "") {
        this.$message({
          type: "error",
          message: "SKU不能为空",
        });
        return false;
      }
      this.axios
        .post("goods/checkSku", {
          info: row,
        })
        .then((response) => {
          if (response.data.errno === 100) {
            this.$message({
              type: "error",
              message: "该SKU已存在！",
            });
          }
        });
    },
    getSpecData() {
      let id = this.infoForm.id;
      this.axios
        .post("goods/getAllProduct", {
          goods_id: id,
        })
        .then((response) => {
          if (response.data.errno === 0) {
            this.specData = response.data.data;
          }
        });
    },
    addSpecData() {
      let pid = this.infoForm.id;
      let goods_sn = "";
      if (pid) {
        // 随机生成一个sn 号
        goods_sn = pid.toString() + Math.floor(Math.random() * 900);

        let ele = {
          goods_sn: goods_sn,
          goods_specifition_name: "",
          market_price: "",
          retail_price: "",
          agent_price: "",
          goods_weight: "",
          goods_number: "",
        };
        this.specData.push(ele);
      }else{
        this.$message({
          type: "error",
          message: "请先保存商品信息",
        });
      }
    },
    specDelete(index, row) {
      this.specData.splice(index, 1);
    },
    specChange(value) {
      this.specForm.id = value;
    },
    addPrimarySpec() {
      this.is_has_spec = true;
    },
    getImgUrl() {
      let str = this.infoForm.goods_desc;
      //匹配图片（g表示匹配所有结果i表示区分大小写）
      let imgReg = /<img [^>]*src=['"]([^'"]+)[^>]*>/gi;
      //匹配src属性
      let srcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/i;
      let arr = str.match(imgReg);
      if (arr == null) {
        return false;
      }
      let data = [];

      for (let i = 0; i < arr.length; i++) {
        let src = arr[i].match(srcReg);
        let jsonData = {};
        jsonData.url = src[1];
        data[i] = jsonData;
      }
      this.detail_list = data;
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    galleryPreview(file) {
      console.log(file);
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    getGalleryList() {
      let goodsId = this.infoForm.id;
      this.axios
        .post("goods/getGalleryList", {
          goodsId: goodsId,
        })
        .then((response) => {
          this.gallery_list = response.data.data.galleryData;
        });
    },
    kdChange(kdValue) {
      this.infoForm.freight_template_id = kdValue;
    },
    onEditorReady(editor) {
      console.log("editor ready!", editor);
    },
    onEditorFocus(editor) {
      console.log("editor focus!", editor);
    },
    onEditorBlur(editor) {
      console.log("editor blur!", editor);
    },
    goBackPage() {
      this.$router.go(-1);
    },
    onCopyGood() {
      this.$confirm("确定复制该商品？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.axios
          .post("goods/copygoods", {
            id: this.infoForm.id,
          })
          .then((response) => {
            if (response.data.errno === 0) {
              this.$message({
                type: "success",
                message: "复制成功!",
              });
            }
          });
      });
    },
    onSubmitInfo() {
      this.$refs["infoForm"].validate((valid) => {
        if (valid) {
          if (
            this.infoForm.list_pic_url == "" ||
            this.infoForm.list_pic_url == null
          ) {
            this.$message({
              type: "error",
              message: "请上传商品首图！",
            });
            return false;
          }
          if (this.gallery_list.length == 0) {
            this.$message({
              type: "error",
              message: "请至少上传一张轮播图！",
            });
            return false;
          }

          for (const ele of this.specData) {
            if (
              ele.market_price == "" ||
              ele.retail_price == "" ||
              ele.goods_specifition_name == "" ||
              ele.agent_price == ""
            ) {
              this.$message({
                type: "error",
                message: "型号和价格的值不能为空",
              });
              return false;
            }
          }
          this.infoForm.gallery = this.gallery_list;
          this.axios
            .post("goods/store", {
              info: this.infoForm,
              specData: this.specData,
              cateId: this.cateId,
            })
            .then((response) => {
              if (response.data.errno === 0) {
                this.$message({
                  type: "success",
                  message: "保存成功",
                });
                this.infoForm.id = response.data.data;
                this.getGalleryList();
              } else {
                this.$message({
                  type: "error",
                  message: "保存失败",
                });
              }
            });
        } else {
          return false;
        }
      });
    },

    getInfo() {
      if (this.infoForm.id <= 0) {
        return false;
      }
      //加载商品详情
      let that = this;
      this.axios
        .get("goods/info", {
          params: {
            id: that.infoForm.id,
          },
        })
        .then((response) => {
          let resInfo = response.data.data;
          let goodsInfo = resInfo.info;
          // goodsInfo.is_index = goodsInfo.is_index ? true : false;
          goodsInfo.is_new = goodsInfo.is_new ? true : false;
          goodsInfo.is_on_sale = goodsInfo.is_on_sale ? "1" : "0";
          that.infoForm = goodsInfo;
          that.kdValue = goodsInfo.freight_template_id == 0 ? '' : goodsInfo.freight_template_id;
          that.cateId = resInfo.category_id || "";
          that.getImgUrl();
        });
    },
    // 获取所有分类
    getAllCategory() {
      let that = this;
      this.axios
        .get("goods/getAllCategory", {
          params: {},
        })
        .then((response) => {
          that.options = response.data.data;
        });
    },
    getExpressData() {
      let that = this;
      this.axios
        .post("goods/getExpressData", {
          freightUseType: 0,
        })
        .then((response) => {
          let options = response.data.data;
          that.kdOptions = options.kd;
          that.cateOptions = options.cate;
        });
    },

    deleteImage(key) {
      let that = this;
      that.$confirm('确定删除该图片?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
      })
      .then(() => {
          that.infoForm[`${key}`] = '';
      })
      .catch(() => {})
    },
    handleUploadImageSuccess(res, key) {
        this.infoForm[`${key}`] = res.highimage;
    },
    handleUploadBannerImageSuccess(res, index) {
      if (index == -1) {
        this.gallery_list.push({
          url: res.highimage,
          is_delete: 0,
          id: 0,
        });
      }else{
        let item = this.gallery_list[index];
        if (item.id == 0) {
          // 数组中直接删掉该元素
          this.gallery_list.splice(index, 1);
        }else{
          item.url = res.highimage;
        }
      }
    },
    deleteBannerImage(index) {
      this.gallery_list[index].is_delete = 1;
    },
    handleUploadDetailImageSuccess(res) {
      for (let i = 0; i < res.length; i++) {
        setTimeout(() => {
          this.handleUploadDetailSuccess(res[i]);
        }, 300);
      }
    },
    handleUploadDetailSuccess(data) {
      let quill = this.$refs.myTextEditor.quill;
      // 如果上传成功
      // 获取光标所在位置
      let length = quill.getSelection().index;
      // 插入图片  res.info为服务器返回的图片地址
      quill.insertEmbed(length, "image", data);
      quill.insertText(length + 1, '\n');
      // 调整光标换行
      quill.setSelection(length + 2);
      // this.$message.error('图片插入失败')
      // loading动画消失
      this.quillUpdateImg = false;
    },

    // 处理视频上传成功
    handleUploadDetailVideoSuccess(res) {
      if (!res || !res.highimage) {
        this.$message.error('视频上传失败');
        return;
      }

      let quill = this.$refs.myTextEditor.quill;
      // 获取光标所在位置
      let length = quill.getSelection() ? quill.getSelection().index : 0;

      // 使用Quill的原生video格式
      quill.insertEmbed(length, 'video', res.highimage);

      // 在视频后添加一个换行
      quill.insertText(length + 1, '\n');

      // 调整光标位置
      quill.setSelection(length + 2);

      this.$message.success('视频上传成功');
    },
  },
  components: {
    quillEditor,
    draggable,
    UploadFileBtn
  },
  computed: {
    editor() {
      return this.$refs.myTextEditor.quillEditor;
    },
  },
  mounted() {
    this.infoForm.id = this.$route.query.id || 0;
    if (this.infoForm.id > 0) {
      this.getSpecData();
      this.getGalleryList();
    }
    this.getGoodsBrand();
    this.getAllCategory();
    this.getExpressData();

    this.getInfo();
  },
};
</script>

<style scoped>
.shadow,
.o-shadow {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s;
  color: #fff;
  font-size: 20px;
  line-height: 20px;
  padding: 10px;
  cursor: pointer;
}

.gallery-item {
  display: flex;
  justify-content: center;
  align-items: center;
  float: left;
  position: relative;
}

.gallery-item:hover .shadow {
  opacity: 1;
}

.video-wrap {
  width: 300px;
}

.dialog-header {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 10px;
}

.dialog-header .value {
  width: 150px;
  margin-right: 14px;
}

.input-wrap .el-input {
  width: 200px;
  float: left;
  margin: 0 20px 20px 0;
}

.input-wrap .el-input input {
  background-color: #fff !important;
  color: #233445 !important;
}

.specFormDialig .el-input {
  width: 150px;
  margin-right: 10px;
}

.el-select-class {
  width: 400px;
  margin-right: 20px;
}

.sepc-form {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 10px;
}

.spec-form-wrap {
  margin-top: 0 !important;
}

.add-spec {
  margin-top: 10px;
}

.spec-form-wrap .header {
  display: flex;
  justify-content: flex-start;
}

.spec-form-wrap .header .l {
  width: 200px;
  margin-right: 20px;
}

.spec-form-wrap .header .m {
  width: 200px;
  margin-right: 20px;
}

.spec-form-wrap .header .m {
  width: 200px;
  margin-right: 20px;
}

/*.sepc-form div{*/
/*margin-left: 0;*/
/*}*/

.float-right {
  float: right;
}

.sepc-form .el-input {
  width: 200px;
  margin-right: 20px;
}

.marginTop20 {
  margin-top: 20px;
}

.checkbox-wrap .checkbox-list {
  float: left;
  margin-right: 20px;
}

.upload_ad {
  display: none;
}

.upload_ad .el-upload--picture-card {
  display: none;
}

/* 确保视频上传组件和图片上传组件都能正确隐藏 */
.video-uploader, .avatar-uploader {
  display: none;
}

.ql-container {
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
}

.image-uploader-diy {
  /*height: 200px;*/
  position: relative;
}

.image-uploader-diy .el-upload {
  border: 1px solid #d9d9d9;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.image-uploader-diy .el-upload:hover {
  border-color: #20a0ff;
}

.image-uploader-diy .image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 200px;
  height: 200px;
  line-height: 200px;
  text-align: center;
}

.image-uploader-diy .image-show {
  min-width: 148px;
  height: 148px;
  background-color: #f9f9f9;
  display: block;
}

.index-image {
  width: 148px;
  height: 148px;
  position: relative;
}

.index-image:hover .o-shadow {
  opacity: 1;
}

.image-uploader-diy .new-image-uploader {
  font-size: 28px;
  color: #8c939d;
  width: 165px;
  height: 105px;
  line-height: 105px;
  text-align: center;
}

.image-uploader-diy .new-image-uploader .image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 165px;
  height: 105px;
  line-height: 105px;
  text-align: center;
}

.image-uploader-diy .new-image-uploader .image-show {
  width: 165px;
  height: 105px;
  display: block;
}

.item-url-image-fuzhu .el-input {
  width: 260px;
}

.hidden {
  display: none;
}
</style>
