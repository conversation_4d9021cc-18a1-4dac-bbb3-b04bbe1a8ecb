<template>
  <div class="content-page">
    <div class="content-nav">
      <el-breadcrumb class="breadcrumb" separator="/">
        <el-breadcrumb-item :to="{ name: 'nature' }"
          >商品分类</el-breadcrumb-item
        >
        <el-breadcrumb-item>{{
          infoForm.id ? "编辑分类" : "添加分类"
        }}</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="operation-nav">
        <el-button type="primary" @click="goBackPage" icon="arrow-left"
          >返回列表</el-button
        >
      </div>
    </div>
    <div class="content-main">
      <div class="form-table-box">
        <el-form
          ref="infoForm"
          :rules="infoRules"
          :model="infoForm"
          label-width="120px"
        >
          <el-form-item
            label="图标"
            prop="icon_url"
            v-if="infoForm.parent_id == 0"
          >
            <div class="image-show" style="width: 100px; height: 100px;">
							<UploadFileBtn :imgUrl="infoForm.icon_url" @getFileUrl="handleUploadIconSuccess" @deleteImage="deleteIconImage" />
						</div>

            <div class="form-tip">
              图片尺寸：图标显示在APP上为35*35, 只能上传jpg/png文件
            </div>
          </el-form-item>
          <el-form-item label="分类名称" prop="name">
            <el-input v-model="infoForm.name"></el-input>
          </el-form-item>
          <el-form-item label="简短介绍">
            <el-input
              type="textarea"
              v-model="infoForm.front_name"
              :rows="1"
            ></el-input>
            <div class="form-tip"></div>
          </el-form-item>
          <el-form-item
            label="分类图片高度"
            prop="p_height"
            v-if="infoForm.parent_id == 0"
          >
            <el-input v-model="infoForm.p_height"></el-input>
          </el-form-item>
          <el-form-item
            label="分类图片"
            v-if="infoForm.parent_id == 0"
          >
            <div class="image-show" :style="{height: infoForm.p_height + 'px'}">
							<UploadFileBtn :imgUrl="infoForm.img_url" @getFileUrl="handleUploadBannerSuccess" @deleteImage="deleteBannerImage" />
						</div>
            <div class="form-tip">
              图片尺寸：建议尺寸为296*66自定义, 只能上传jpg/png文件
            </div>
          </el-form-item>
          
          <el-form-item label="排序">
            <el-input-number
              v-model="infoForm.sort_order"
              :min="1"
              :max="1000"
            ></el-input-number>
          </el-form-item>

          <!-- <el-form-item label="分类品牌">
            <el-button class="marginTop20" type="primary" @click="addSubCateData"
                >新增品牌</el-button
              >
            <div class="spec-wrap">
              <el-table :data="subCateData" stripe style="width: 100%">
                <el-table-column
                  prop="name"
                  label="品牌名称"
                  width="360"
                >
                  <template slot-scope="scope">
                    <el-input
                      size="mini"
                      v-model="scope.row.name"
                      placeholder="品牌名称"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column 
                  prop="sort_order" 
                  label="排序"
                  width="200"
                >
                  <template slot-scope="scope">
                    <el-input-number
                      size="mini"
                      v-model="scope.row.sort_order"
                      :min="1"
                      :max="1000"
                    ></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column 
                  prop="is_show" 
                  label="是否上架"
                  width="160"
                >
                  <template slot-scope="scope">
                    <el-switch
                      v-model="scope.row.is_show"
                      active-value="1"
                      inactive-value="0"
                      @change='changeSubCategoryStatus($event,scope.row)'>
                    </el-switch>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="70">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="danger"
                      icon="el-icon-delete"
                      circle
                      @click="subCateDataDelete(scope.$index, scope.row)"
                    >
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" @click="onSubmitInfo">确定保存</el-button>
            <el-button @click="goBackPage">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import api from "@/config/api";
import UploadFileBtn from '@/components/UploadFileBtn.vue'
export default {
  components: {
    UploadFileBtn
  },
  data() {
    return {
      infoForm: {
        id: 0,
        name: "",
        parent_id: 0,
        front_name: "",
        img_url: "",
        sort_order: 100,
        icon_url: "",
        p_height: 66,
        // is_show: true,
      },
      infoRules: {
        name: [{ required: true, message: "请输入名称", trigger: "blur" }],
        front_name: [
          { required: true, message: "请输入简介", trigger: "blur" },
        ],
        // img_url: [
        //   { required: true, message: "请选择分类图片", trigger: "blur" },
        // ],
        icon_url: [
          { required: true, message: "请选择分类图标", trigger: "blur" },
        ],
      },
      subCateData: [],
    };
  },
  methods: {
    goBackPage() {
      this.$router.go(-1);
    },
    onSubmitInfo() {
      this.infoForm.level = this.infoForm.parent_id == 0 ? "L1" : "L2";
      console.log(this.infoForm.level);
      this.$refs["infoForm"].validate((valid) => {
        if (valid) {
          this.axios.post("category/store", {
            ...this.infoForm,
            // sub_category: this.subCateData,
          }).then((response) => {
            if (response.data.errno === 0) {
              this.$message({
                type: "success",
                message: "保存成功",
              });
              this.$router.go(-1);
            } else {
              this.$message({
                type: "error",
                message: "保存失败",
              });
            }
          });
        } else {
          return false;
        }
      });
    },
    handleUploadBannerSuccess(res) {
      this.infoForm.img_url = res.highimage;
    },
    deleteBannerImage() {
      let that = this;
      that.$confirm('确定删除该图片?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
      })
      .then(() => {
          that.infoForm.img_url = '';
      })
      .catch(() => {})
    },
    handleUploadIconSuccess(res) {
      this.infoForm.icon_url = res.highimage;
    },
    deleteIconImage() {
      let that = this;
      that.$confirm('确定删除该图片?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
      })
      .then(() => {
          that.infoForm.icon_url = '';
      })
      .catch(() => {})
    },

    getInfo() {
      if (this.infoForm.id <= 0) {
        return false;
      }
      //加载分类详情
      let that = this;
      this.axios
        .get("category/info", {
          params: {
            id: that.infoForm.id,
          },
        })
        .then((response) => {
          let resInfo = response.data.data;
          that.infoForm = resInfo.data;
          // that.subCateData = [].concat(resInfo.subCateData);
        });
    },

    addSubCateData() {
      let maxSort = this.subCateData.length > 0
        ? Math.max(...this.subCateData.map(o => o.sort_order))
        : 0;
      this.subCateData.push({
        name: "",
        sort_order: maxSort + 1,
        is_show: "1",
        parent_id: this.infoForm.id,
        level: "L2",
      });
    },

    changeSubCategoryStatus($event, row) {

    },

    subCateDataDelete(index, row) {
      this.subCateData.splice(index, 1);
    }
  },
  mounted() {
    this.infoForm.id = this.$route.query.id || 0;
    this.getInfo();
  },
};
</script>

<style scoped>
.image-uploader {
  height: 105px;
}

.image-uploader .el-upload {
  border: 1px solid #d9d9d9;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.image-uploader .el-upload:hover {
  border-color: #20a0ff;
}

.image-uploader .image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  min-width: 105px;
  height: 105px;
  line-height: 105px;
  text-align: center;
}

.image-show {
  background-color: #f8f8f8;
  width: 296px;
  height: 66px;
  display: block;
}
</style>
