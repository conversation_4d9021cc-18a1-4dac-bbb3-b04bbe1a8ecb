<template>
    <div class="content-page">
        <div class="content-nav">
            <el-breadcrumb class="breadcrumb" separator="/">
                <el-breadcrumb-item>快递模板</el-breadcrumb-item>
            </el-breadcrumb>
            <div class="operation-nav">
                <!--<el-button icon="plus" @click="expressList">快递列表</el-button>-->
                <!-- <el-button icon="plus" @click="exceptareaList">偏远地区</el-button> -->
                <el-button type="primary" icon="plus" @click="addFreightTemplate">添加快递模板</el-button>
            </div>
        </div>
        <div class="content-main">
            <div class="form-table-box">
                <el-table :data="tableData" style="width: 100%" border stripe>
                    <el-table-column prop="id" label="ID" width="100px"></el-table-column>
                    <el-table-column prop="name" label="名字"></el-table-column>
                    <el-table-column prop="freight_use_type" label="生效范围（专属模版生效必须选择商品，当某个商品设置了专属模版，则该模版生效，其他模版不生效）">
                        <template slot-scope="scope">
                            <!-- 模版生效范围：0 专属，1：用户生效 2：代理商生效 3：全部生效 -->
                            {{ scope.row.freight_use_type == 0 ? '专属模版' : scope.row.freight_use_type == 1 ? '用户生效' : scope.row.freight_use_type == 2 ? '代理商生效' : '全部生效' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="freight_type" label="按件/按重" width="100">
                        <template slot-scope="scope">
                            {{ scope.row.freight_type == 0 ? '按件' : '按重' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="200">
                        <template slot-scope="scope">
                            <el-button size="small" @click="handleRowEdit(scope.$index, scope.row)">编辑</el-button>
                            <el-button size="small" type="danger" @click="handleRowDelete(scope.$index, scope.row)">删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </div>
</template>

<script>

    export default {
        data() {
            return {
                tableData: []
            }
        },
        methods: {
            expressList(){
                this.$router.push({name: 'shipper'});
            },
            exceptareaList(){
                this.$router.push({name: 'except_area'});
            },
            addFreightTemplate(){
                this.$router.push({name: 'freight_add'});
            },
            handleRowEdit(index, row) {
                this.$router.push({name: 'freight_add', query: {id: row.id}})
            },
            handleRowDelete(index, row) {
                this.$confirm('确定要删除?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.axios.post('shipper/destory', {id: row.id}).then((response) => {
                        console.log(response.data)
                        if (response.data.errno === 0) {
                            this.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                            this.getList();
                        }
                    })


                });
            },
            getList() {
                this.axios.get('shipper/freight').then((response) => {
                    console.log(response);
                    this.tableData = response.data.data
                })
            }
        },
        components: {},
        mounted() {
            this.getList();
        }
    }

</script>

<style scoped>

</style>
