<template>
    <div class="content-page">
        <div class="content-nav">
            <el-breadcrumb class="breadcrumb" separator="/">
                <el-breadcrumb-item>商城后台首页</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="content-main clearfix">
            <!-- 1. 顶部核心指标看板 -->
            <div class="top-stats clearfix">
                <el-card class="stat-card card-red">
                    <div class="stat-content">
                        <div class="stat-number">{{dashboardData.total.pendingOrders}}</div>
                        <div class="stat-name">待发货订单</div>
                        <div class="stat-compare">
                            <span class="compare-label">较昨日</span>
                            <span v-if="dashboardData.yesterday" :class="getTrendClass('pendingOrders')">
                                <i :class="getTrendIcon('pendingOrders')"></i>
                                {{getTrendText('pendingOrders')}}
                            </span>
                        </div>
                    </div>
                </el-card>
                <el-card class="stat-card card-green">
                    <div class="stat-content">
                        <div class="stat-number">{{dashboardData.total.totalProducts}}</div>
                        <div class="stat-name">在售商品数</div>
                        <div class="stat-compare">
                            <span class="compare-label">较昨日</span>
                            <span v-if="dashboardData.yesterday" :class="getTrendClass('totalProducts')">
                                <i :class="getTrendIcon('totalProducts')"></i>
                                {{getTrendText('totalProducts')}}
                            </span>
                        </div>
                    </div>
                </el-card>
                <el-card class="stat-card card-blue">
                    <div class="stat-content">
                        <div class="stat-number">{{dashboardData.total.totalUsers}}</div>
                        <div class="stat-name">总用户数</div>
                        <div class="stat-compare">
                            <span class="compare-label">较昨日</span>
                            <span v-if="dashboardData.yesterday" :class="getTrendClass('totalUsers')">
                                <i :class="getTrendIcon('totalUsers')"></i>
                                {{getTrendText('totalUsers')}}
                            </span>
                        </div>
                    </div>
                </el-card>
            </div>

            <!-- 2. 核心数据统计与转化分析看板 -->
            <div class="main-stats">
                <el-card class="main-stats-card">
                    <div slot="header" class="clearfix">
                        <span class="section-title">核心数据统计与转化分析</span>
                        <el-tabs class="time-tabs" v-model="activeName" @tab-click="handleTimeClick">
                            <el-tab-pane label="今天" name="today"></el-tab-pane>
                            <el-tab-pane label="昨天" name="yesterday"></el-tab-pane>
                            <el-tab-pane label="近7天" name="last7Days"></el-tab-pane>
                            <el-tab-pane label="近30天" name="last30Days"></el-tab-pane>
                        </el-tabs>
                    </div>
                    <div class="main-stats-content clearfix">
                        <!-- 第一行：核心数据统计 -->
                        <div class="stats-row clearfix">
                            <!-- 顾客数据 -->
                            <div class="stats-block">
                                <h4 class="block-title">顾客</h4>
                                <div class="stats-items">
                                    <div class="stats-item">
                                        <span class="item-label">新增顾客</span>
                                        <span class="item-value">{{currentTimeData.newUser}}</span>
                                    </div>
                                    <div class="stats-item">
                                        <span class="item-label">老顾客</span>
                                        <span class="item-value">{{currentTimeData.oldUser}}</span>
                                    </div>
                                    <div class="stats-item" v-if="activeName === 'today'">
                                        <span class="item-label">次日留存率</span>
                                        <span class="item-value">{{getRetentionRate()}}%</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 下单数据 -->
                            <div class="stats-block">
                                <h4 class="block-title">下单</h4>
                                <div class="stats-items">
                                    <div class="stats-item">
                                        <span class="item-label">加入购物车</span>
                                        <span class="item-value">{{currentTimeData.addCart}}</span>
                                    </div>
                                    <div class="stats-item">
                                        <span class="item-label">下单总数</span>
                                        <span class="item-value">{{currentTimeData.addOrderNum}}</span>
                                    </div>
                                    <div class="stats-item">
                                        <span class="item-label">下单总金额</span>
                                        <span class="item-value">¥{{currentTimeData.addOrderSum | currency}}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 支付数据 -->
                            <div class="stats-block">
                                <h4 class="block-title">支付</h4>
                                <div class="stats-items">
                                    <div class="stats-item">
                                        <span class="item-label">成交订单数</span>
                                        <span class="item-value">{{currentTimeData.payOrderNum}}</span>
                                    </div>
                                    <div class="stats-item">
                                        <span class="item-label">成交总金额</span>
                                        <span class="item-value">¥{{currentTimeData.payOrderSum | currency}}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="stats-divider"></div>
                        
                        <!-- 第二行：关键转化数据 -->
                        <div class="conversion-row clearfix">
                            <div class="conversion-title">
                                <h4>关键转化数据</h4>
                            </div>
                            <div class="conversion-items clearfix">
                                <div class="conversion-item">
                                    <div class="conversion-value">¥{{getAverageOrderValue() | currency}}</div>
                                    <div class="conversion-label">客单价</div>
                                    <div class="conversion-formula">成交总金额 / 成交订单数</div>
                                </div>
                                <!--<div class="conversion-item">
                                    <div class="conversion-value">{{getOrderConversionRate()}}%</div>
                                    <div class="conversion-label">下单转化率</div>
                                    <div class="conversion-formula">下单总数 / 访问人数</div>
                                </div>-->
                                <div class="conversion-item">
                                    <div class="conversion-value">{{getPaymentConversionRate()}}%</div>
                                    <div class="conversion-label">支付转化率</div>
                                    <div class="conversion-formula">成交订单数 / 下单总数</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-card>
            </div>

            <!-- 3. 数据趋势图表 -->
            <div class="trend-charts">
                <el-card class="chart-card">
                    <div slot="header" class="clearfix">
                        <span class="section-title">订单与销售额趋势（近30天）</span>
                    </div>
                    <div class="chart-content">
                        <div ref="orderTrendChart" class="chart-container"></div>
                    </div>
                </el-card>
                <el-card class="chart-card" style="margin-top: 20px;">
                    <div slot="header" class="clearfix">
                        <span class="section-title">用户增长趋势（近30天）</span>
                    </div>
                    <div class="chart-content">
                        <div ref="userTrendChart" class="chart-container"></div>
                    </div>
                </el-card>
            </div>

            <!-- 5. 待处理任务清单 -->
            <!-- <div class="task-lists">
                <div class="task-row clearfix">
                    <el-card class="task-card">
                        <div slot="header" class="clearfix">
                            <span class="section-title">待发货订单</span>
                            <router-link :to="{ path: '/dashboard/order' }" class="view-more">查看更多</router-link>
                        </div>
                        <el-table :data="dashboardData.pendingOrderList" style="width: 100%" max-height="300">
                            <el-table-column prop="orderNo" label="订单号" width="150"></el-table-column>
                            <el-table-column prop="customerName" label="客户" width="120"></el-table-column>
                            <el-table-column prop="orderAmount" label="订单金额" width="120">
                                <template slot-scope="scope">
                                    ¥{{scope.row.orderAmount | currency}}
                                </template>
                            </el-table-column>
                            <el-table-column prop="orderTime" label="下单时间" width="160"></el-table-column>
                            <el-table-column label="操作" width="100">
                                <template slot-scope="scope">
                                    <el-button type="text" size="small" @click="goToOrderDetail(scope.row.orderId)">查看详情</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-card>
                    
                    <el-card class="task-card">
                        <div slot="header" class="clearfix">
                            <span class="section-title">新用户注册</span>
                            <router-link :to="{ path: '/dashboard/user' }" class="view-more">查看更多</router-link>
                        </div>
                        <el-table :data="dashboardData.newUserList" style="width: 100%" max-height="300">
                            <el-table-column prop="username" label="用户名" width="120"></el-table-column>
                            <el-table-column prop="phone" label="手机号" width="130"></el-table-column>
                            <el-table-column prop="registerTime" label="注册时间" width="160"></el-table-column>
                            <el-table-column prop="status" label="状态" width="80">
                                <template slot-scope="scope">
                                    <el-tag :type="scope.row.status === '已验证' ? 'success' : 'warning'" size="small">
                                        {{scope.row.status}}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="100">
                                <template slot-scope="scope">
                                    <el-button type="text" size="small" @click="goToUserDetail(scope.row.userId)">查看详情</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-card>
                </div>
            </div> -->
        </div>
    </div>
</template>

<script>
import Countdown from './Common/Countdown';
import * as echarts from 'echarts';

export default {
    data() {
        return {
            dashboardData: {
                total: {
                    pendingOrders: 0,
                    totalProducts: 0,
                    totalUsers: 0
                },
                yesterday: {
                    pendingOrders: 0,
                    totalProducts: 0,
                    totalUsers: 0
                },
                timeData: {
                    today: {
                        newUser: 0,
                        oldUser: 0,
                        addCart: 0,
                        addOrderNum: 0,
                        addOrderSum: 0,
                        payOrderNum: 0,
                        payOrderSum: 0,
                        visitUsers: 0
                    },
                    yesterday: {
                        newUser: 0,
                        oldUser: 0,
                        addCart: 0,
                        addOrderNum: 0,
                        addOrderSum: 0,
                        payOrderNum: 0,
                        payOrderSum: 0,
                        visitUsers: 0
                    },
                    last7Days: {
                        newUser: 0,
                        oldUser: 0,
                        addCart: 0,
                        addOrderNum: 0,
                        addOrderSum: 0,
                        payOrderNum: 0,
                        payOrderSum: 0,
                        visitUsers: 0
                    },
                    last30Days: {
                        newUser: 0,
                        oldUser: 0,
                        addCart: 0,
                        addOrderNum: 0,
                        addOrderSum: 0,
                        payOrderNum: 0,
                        payOrderSum: 0,
                        visitUsers: 0
                    }
                },
                pendingOrderList: [],
                newUserList: []
            },
            activeName: 'today',
            loginInfo: null,
            username: ''
        }
    },
    computed: {
        currentTimeData() {
            return this.dashboardData.timeData[this.activeName] || {};
        }
    },
    methods: {
        getDashboardData() {
            this.axios.get('index/newMain').then((response) => {
                if (response.data.errno === 0) {
                    this.dashboardData = response.data.data;
                }
            }).catch(() => {
                
            });
        },
        handleTimeClick(tab) {
            this.activeName = tab.name;
        },
        getRetentionRate() {
            const yesterdayNewUsers = this.dashboardData.timeData.yesterday.newUser;
            if (yesterdayNewUsers === 0) return 0;
            const retainedUsers = Math.floor(this.dashboardData.timeData.today.oldUser * 0.1);
            return ((retainedUsers / yesterdayNewUsers) * 100).toFixed(1);
        },
        getAverageOrderValue() {
            const data = this.currentTimeData;
            if (!data.payOrderNum || data.payOrderNum === 0) {
                return 0;
            }
            return (data.payOrderSum / data.payOrderNum);
        },
        getOrderConversionRate() {
            const data = this.currentTimeData;
            if (!data.visitUsers || data.visitUsers === 0) {
                return '暂无数据';
            }
            return ((data.addOrderNum / data.visitUsers) * 100).toFixed(1);
        },
        getPaymentConversionRate() {
            const data = this.currentTimeData;
            if (!data.addOrderNum || data.addOrderNum === 0) {
                return 0;
            }
            return ((data.payOrderNum / data.addOrderNum) * 100).toFixed(1);
        },
        getTrendText(key) {
            const current = this.dashboardData.total[key] || 0;
            const yesterday = this.dashboardData.yesterday ? this.dashboardData.yesterday[key] || 0 : 0;
            if (yesterday === 0) return '';
            const change = current - yesterday;
            const percentage = ((Math.abs(change) / yesterday) * 100).toFixed(1);
            if (change > 0) {
                return `+${percentage}%`;
            } else if (change < 0) {
                return `-${percentage}%`;
            }
            return '0%';
        },
        getTrendClass(key) {
            const current = this.dashboardData.total[key] || 0;
            const yesterday = this.dashboardData.yesterday ? this.dashboardData.yesterday[key] || 0 : 0;
            const change = current - yesterday;
            if (change > 0) {
                return 'trend-up';
            } else if (change < 0) {
                return 'trend-down';
            }
            return 'trend-stable';
        },
        getTrendIcon(key) {
            const current = this.dashboardData.total[key] || 0;
            const yesterday = this.dashboardData.yesterday ? this.dashboardData.yesterday[key] || 0 : 0;
            const change = current - yesterday;
            if (change > 0) {
                return 'el-icon-arrow-up';
            } else if (change < 0) {
                return 'el-icon-arrow-down';
            }
            return 'el-icon-minus';
        },
        goToOrderDetail(orderId) {
            this.$router.push({ path: '/dashboard/order/detail', query: { id: orderId } });
        },
        goToUserDetail(userId) {
            this.$router.push({ path: '/dashboard/user/detail', query: { id: userId } });
        },
        // 初始化 ECharts 图表
        initCharts() {
            this.$nextTick(() => {
                this.initOrderTrendChart();
                this.initUserTrendChart();
            });
        },
        // 初始化订单与销售额趋势图
        initOrderTrendChart() {
            const chart = echarts.init(this.$refs.orderTrendChart);
            
            // 模拟近30天数据
            const dates = [];
            const orderNums = [];
            const salesAmounts = [];
            
            for (let i = 29; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                dates.push(date.getMonth() + 1 + '/' + date.getDate());
                orderNums.push(Math.floor(Math.random() * 20) + 5);
                salesAmounts.push(Math.floor(Math.random() * 5000) + 1000);
            }
            
            const option = {
                title: {
                    text: '近30天订单与销售额趋势',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        color: '#2d3436'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '8%',
                    top: '15%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    formatter: function(params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(param => {
                            const unit = param.seriesName === '成交订单数' ? '单' : '元';
                            result += param.marker + param.seriesName + ': ' + param.value + unit + '<br/>';
                        });
                        return result;
                    }
                },
                legend: {
                    data: ['成交订单数', '成交金额'],
                    top: 30
                },
                xAxis: {
                    type: 'category',
                    data: dates,
                    axisLine: {
                        lineStyle: {
                            color: '#e9ecef'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: '#636e72',
                        fontSize: 12,
                        interval: 0,
                        rotate: 45
                    },
                    boundaryGap: true
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '订单数',
                        position: 'left',
                        axisLine: {
                            lineStyle: {
                                color: '#74b9ff'
                            }
                        },
                        axisLabel: {
                            color: '#636e72'
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#f1f3f4'
                            }
                        }
                    },
                    {
                        type: 'value',
                        name: '金额',
                        position: 'right',
                        axisLine: {
                            lineStyle: {
                                color: '#00b894'
                            }
                        },
                        axisLabel: {
                            color: '#636e72',
                            formatter: '{value}元'
                        }
                    }
                ],
                series: [
                    {
                        name: '成交订单数',
                        type: 'line',
                        yAxisIndex: 0,
                        data: orderNums,
                        smooth: true,
                        lineStyle: {
                            color: '#74b9ff',
                            width: 3
                        },
                        itemStyle: {
                            color: '#74b9ff'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: 'rgba(116, 185, 255, 0.3)' },
                                { offset: 1, color: 'rgba(116, 185, 255, 0.1)' }
                            ])
                        }
                    },
                    {
                        name: '成交金额',
                        type: 'line',
                        yAxisIndex: 1,
                        data: salesAmounts,
                        smooth: true,
                        lineStyle: {
                            color: '#00b894',
                            width: 3
                        },
                        itemStyle: {
                            color: '#00b894'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: 'rgba(0, 184, 148, 0.3)' },
                                { offset: 1, color: 'rgba(0, 184, 148, 0.1)' }
                            ])
                        }
                    }
                ]
            };
            
            chart.setOption(option);
            
            // 响应式处理
            window.addEventListener('resize', () => {
                chart.resize();
            });
        },
        // 初始化用户增长趋势图
        initUserTrendChart() {
            const chart = echarts.init(this.$refs.userTrendChart);
            
            // 模拟近30天数据
            const dates = [];
            const newUsers = [];
            const totalUsers = [];
            
            let currentTotal = 350; // 初始用户数
            
            for (let i = 29; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                dates.push(date.getMonth() + 1 + '/' + date.getDate());
                
                const newUserCount = Math.floor(Math.random() * 15) + 2;
                newUsers.push(newUserCount);
                currentTotal += newUserCount;
                totalUsers.push(currentTotal);
            }
            
            const option = {
                title: {
                    text: '近30天用户增长趋势',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        color: '#2d3436'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '8%',
                    top: '15%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    formatter: function(params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(param => {
                            result += param.marker + param.seriesName + ': ' + param.value + '人<br/>';
                        });
                        return result;
                    }
                },
                legend: {
                    data: ['新增用户', '累计用户'],
                    top: 30
                },
                xAxis: {
                    type: 'category',
                    data: dates,
                    axisLine: {
                        lineStyle: {
                            color: '#e9ecef'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: '#636e72',
                        fontSize: 12,
                        interval: 0,
                        rotate: 45
                    },
                    boundaryGap: true
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '新增用户',
                        position: 'left',
                        axisLine: {
                            lineStyle: {
                                color: '#ff7675'
                            }
                        },
                        axisLabel: {
                            color: '#636e72'
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#f1f3f4'
                            }
                        }
                    },
                    {
                        type: 'value',
                        name: '累计用户',
                        position: 'right',
                        axisLine: {
                            lineStyle: {
                                color: '#a29bfe'
                            }
                        },
                        axisLabel: {
                            color: '#636e72'
                        }
                    }
                ],
                series: [
                    {
                        name: '新增用户',
                        type: 'bar',
                        yAxisIndex: 0,
                        data: newUsers,
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: '#ff7675' },
                                { offset: 1, color: '#fd79a8' }
                            ])
                        },
                        barWidth: '60%'
                    },
                    {
                        name: '累计用户',
                        type: 'line',
                        yAxisIndex: 1,
                        data: totalUsers,
                        smooth: true,
                        lineStyle: {
                            color: '#a29bfe',
                            width: 3
                        },
                        itemStyle: {
                            color: '#a29bfe'
                        }
                    }
                ]
            };
            
            chart.setOption(option);
            
            // 响应式处理
            window.addEventListener('resize', () => {
                chart.resize();
            });
        }
    },
    components: {
        Countdown
    },
    mounted() {
        this.getDashboardData();
        if (!this.loginInfo) {
            this.loginInfo = JSON.parse(window.localStorage.getItem('userInfo') || 'null');
            if (this.loginInfo) {
                this.username = this.loginInfo.username;
            }
        }
        // 初始化图表
        this.initCharts();
    },
    filters: {
        currency(value) {
            if (!value || value === 0) return '0.00';
            return Number(value).toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }
    }
}
</script>

<style scoped>
.content-page {
    padding: 20px;
    background: #f5f5f5;
    min-height: 100vh;
}

.content-nav {
    margin-bottom: 20px;
}

.clearfix:before,
.clearfix:after {
    display: table;
    content: "";
}

.clearfix:after {
    clear: both;
}

/* 顶部核心指标看板样式 */
.top-stats {
    margin-bottom: 30px;
}

.stat-card {
    width: 32%;
    float: left;
    margin-right: 2%;
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.stat-card:last-child {
    margin-right: 0;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.card-red {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.card-green {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
}

.card-blue {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
}

.stat-content {
    padding: 20px;
}

.stat-number {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 10px;
}

.stat-name {
    font-size: 16px;
    margin-bottom: 15px;
    opacity: 0.9;
}

.stat-compare {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.compare-label {
    opacity: 0.8;
}

.trend-up {
    color: #00b894;
}

.trend-down {
    color: #e17055;
}

.trend-stable {
    color: #636e72;
}

/* 核心数据统计看板样式 */
.main-stats {
    margin-bottom: 30px;
}

.main-stats-card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.section-title {
    font-size: 18px;
    font-weight: bold;
    color: #2d3436;
}

.time-tabs {
    float: right;
}

.main-stats-content {
    padding: 20px 0;
}
    
.stats-row {
    margin-bottom: 25px;
}
    
.stats-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, #e9ecef, transparent);
    margin: 25px 0;
}
    
.conversion-row {
    padding-top: 15px;
}
    
.conversion-title {
    text-align: center;
    margin-bottom: 20px;
}
    
.conversion-title h4 {
    font-size: 16px;
    color: #2d3436;
    margin: 0;
    font-weight: bold;
}
    
.conversion-items {
    display: flex;
    justify-content: space-between;
    gap: 15px;
}

.stats-block {
    width: 30%;
    float: left;
    margin-right: 5%;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
}

.stats-block:last-child {
    margin-right: 0;
}

.block-title {
    font-size: 16px;
    font-weight: bold;
    color: #2d3436;
    margin-bottom: 15px;
    text-align: center;
    border-bottom: 2px solid #74b9ff;
    padding-bottom: 8px;
}

.stats-items {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.stats-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.stats-item:last-child {
    border-bottom: none;
}

.item-label {
    font-size: 14px;
    color: #636e72;
}

.item-value {
    font-size: 16px;
    font-weight: bold;
    color: #2d3436;
}

/* 关键转化数据看板样式 */
.conversion-stats {
    margin-bottom: 30px;
}

.conversion-card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.conversion-content {
    padding: 20px 0;
}

.conversion-item {
    width: 30%;
    float: left;
    margin-right: 5%;
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.conversion-item:last-child {
    margin-right: 0;
}

.conversion-value {
    font-size: 32px;
    font-weight: bold;
    color: #74b9ff;
    margin-bottom: 10px;
}

.conversion-label {
    font-size: 16px;
    font-weight: bold;
    color: #2d3436;
    margin-bottom: 8px;
}

.conversion-formula {
    font-size: 12px;
    color: #636e72;
}

/* 数据趋势图表样式 */
.trend-charts {
    margin-bottom: 30px;
}

.chart-row {
    display: flex;
    gap: 20px;
}

.chart-card {
    flex: 1;
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-content {
    padding: 0;
    height: 350px;
    width: 100%;
}

.chart-container {
    height: 100%;
    width: 100%;
}
    
/* 关键转化数据样式（内嵌在主看板中） */
.conversion-item {
    flex: 1;
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
}
    
.conversion-item:hover {
    background: #e9ecef;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
    
.conversion-value {
    font-size: 28px;
    font-weight: bold;
    color: #74b9ff;
    margin-bottom: 8px;
}
    
.conversion-label {
    font-size: 14px;
    font-weight: bold;
    color: #2d3436;
    margin-bottom: 6px;
}
    
.conversion-formula {
    font-size: 11px;
    color: #636e72;
}

/* 待处理任务清单样式 */
.task-lists {
    margin-bottom: 30px;
}

.task-row {
    display: flex;
    gap: 20px;
}

.task-card {
    flex: 1;
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.view-more {
    color: #74b9ff;
    text-decoration: none;
    font-size: 14px;
}

.view-more:hover {
    color: #0984e3;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stat-card, .stats-block, .conversion-item {
        width: 100%;
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .chart-row, .task-row {
        flex-direction: column;
    }
    
    .time-tabs {
        float: none;
        margin-top: 10px;
    }
}
</style>
