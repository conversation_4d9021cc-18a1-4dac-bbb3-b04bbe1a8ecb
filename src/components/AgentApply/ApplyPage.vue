<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-07 18:46:54
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-08 01:32:28
 * @FilePath: /petshop-admin/src/components/AgentApply/ApplyPage.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
v<template>
  <div class="content-page">
    <div class="content-nav">
      <el-breadcrumb class="breadcrumb" separator="/">
        <el-breadcrumb-item>申请列表</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="content-main">
      <div class="filter-box">
        <el-form :inline="true" :model="filterForm" class="demo-form-inline">
          <el-form-item label="申请时间">
            <el-input
              class="filter-input"
              v-model="filterForm.keyword"
              placeholder="请输入申请人名称，昵称，联系电话"
            ></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="filterForm.review_status" placeholder="请选择">
              <el-option label="全部" value=""></el-option>
              <el-option label="待审核" value="0"></el-option>
              <el-option label="已通过" value="1"></el-option>
              <el-option label="已拒绝" value="2"></el-option>
            </el-select>
            </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmitFilter">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="form-table-box">
        <el-table :data="tableData" style="width: 100%" border stripe>
          <el-table-column prop="id" label="ID" width="60">
          </el-table-column>
          <el-table-column prop="user_id" label="用户ID" width="100">
          </el-table-column>
          <el-table-column prop="nickname" label="用户昵称">
          </el-table-column>
          <el-table-column prop="shop_name" label="店铺名称">
          </el-table-column>
          <el-table-column prop="create_time" label="申请时间" width="180">
          </el-table-column>
          <el-table-column prop="review_status" label="状态" width="180">
            <template slot-scope="scope">
              <label style="color: orange;" v-if="scope.row.review_status == 0">待审核</label>
              <label style="color: green;" v-if="scope.row.review_status == 1">已通过</label>
              <label style="color: red;" v-if="scope.row.review_status == 2">已拒绝</label>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="330">
            <template slot-scope="scope">
              <el-button size="small" @click="handleRowEdit(scope.$index, scope.row)">查看</el-button>
              <el-button v-if="scope.row.review_status == 0" size="small" type="primary" @click="handleRowPass(scope.$index, scope.row)">通过</el-button>
              <el-button v-if="scope.row.review_status == 0" size="small" type="warning" @click="handleRowRefuse(scope.$index, scope.row)">拒绝</el-button>
              <el-button size="small" type="danger" @click="handleRowDelete(scope.$index, scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="page-box">
        <el-pagination
          @current-change="handlePageChange"
          :current-page="page"
          :page-size="10"
          layout="total, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 详情弹窗 -->
     <el-dialog title="申请详情" :visible.sync="dialogVisible" width="30%">
        <!-- 店铺信息 busi_lices_url shop_name shop_link_name shop_link_phone review_status -->
        <el-form :model="applyDetail">
            <el-form-item label="店铺名称：" label-width="120px">
                <label>{{applyDetail.shop_name}}</label>
            </el-form-item>
            <el-form-item label="营业执照：" label-width="120px">
                <el-image 
                    style="width: 200px; height: 200px"
                    :src="applyDetail.busi_lices_url"
                    :preview-src-list="[applyDetail.busi_lices_url]">
                </el-image>
            </el-form-item>
            <el-form-item label="联系人：" label-width="120px">
                <label>{{applyDetail.shop_link_name}}</label>
            </el-form-item>
            <el-form-item label="联系电话：" label-width="120px">
                <label>{{applyDetail.shop_link_phone}}</label>
            </el-form-item>
            <el-form-item label="申请时间：" label-width="120px">
                <label>{{applyDetail.create_time}}</label>
            </el-form-item>
            <!-- 地址 -->
             <el-form-item label="地址：" label-width="120px">
                <label>{{applyDetail.province_name}}{{applyDetail.city_name}}{{applyDetail.district_name}}{{applyDetail.address}}</label>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
        </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      page: 1,
      total: 0,
      filterForm: {
        keyword: "",
        review_status: ""
      },
      tableData: [],
      applyDetail: {},
      dialogVisible: false,
    };
  },
  methods: {
    handlePageChange(val) {
      this.page = val;
      this.getList();
    },
    handleRowEdit(index, row) {
      this.applyDetail = row;
      this.dialogVisible = true;
    },
    onSubmitFilter() {
      this.page = 1;
      this.getList();
    },
    getList() {
      this.axios
        .post("/agentApply/list", {
            page: this.page,
            keyword: this.filterForm.keyword,
            review_status: this.filterForm.review_status
        })
        .then((response) => {
          this.tableData = response.data.data.data;
          this.page = response.data.data.currentPage;
          this.total = response.data.data.count;
        });
    },
    handleRowPass(index, row) {
        this.$confirm('确定要通过审核吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            this.axios.post('/agentApply/review', {id: row.id, review_status: 1}).then((response) => {
            if (response.data.errno === 0) {
                this.$message({
                type: 'success',
                message: '操作成功!'
                });
                this.getList();
            }
            })
        });
    },
    handleRowRefuse(index, row) {
        this.$prompt('确定要审核拒绝吗？', "提示", {
            inputPlaceholder:'请输入拒绝原因',
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            closeOnClickModal: false,
            inputValidator: (value)=>{ //带有监听功能监听输入value值 触发return false时输入框会提示inputErrorMessage的内容
                
            },
            type: "warning",
        }).then((e) => {
            this.axios.post('/agentApply/review', {id: row.id, review_status: 2, refuse_reason: e.value}).then((response) => {
                if (response.data.errno === 0) {
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                    this.getList();
                }
            })
        });
    },
    handleRowDelete(index, row) {
      this.$confirm('确定要删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.axios.post('/agentApply/delete', {id: row.id}).then((response) => {
          if (response.data.errno === 0) {
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
            this.getList();
          }
        })
      });
    }
  },
  mounted() {
    this.getList();
  }
};
</script>
<style scoped>
</style>
