<template>
    <div class="content-page">
        <div class="content-nav">
            <el-breadcrumb class="breadcrumb" separator="/">
                <el-breadcrumb-item>快递设置</el-breadcrumb-item>
            </el-breadcrumb>
            <div class="operation-nav">
                <router-link to="/dashboard/shipper/list">
                    <el-button type="primary" icon="plus">快递公司列表</el-button>
                </router-link>
            </div>
        </div>
        <div class="content-main">
            <div class="form-table-box">
                <el-form ref="infoForm" :model="infoForm" :rules="infoRules" label-width="120px">
                    <!-- <el-form-item label="打印后自动发货">
                        <el-switch v-model="infoForm.auto_delivery" :active-value="1" :inactive-value="0"></el-switch>
                    </el-form-item> -->
                    <el-form-item label="寄件人" prop="name">
                        <el-input v-model="infoForm.name" placeholder="请输入非代理发货时的寄件人"></el-input>
                    </el-form-item>
                    <el-form-item label="电话" prop="tel">
                        <el-input v-model="infoForm.tel" placeholder="请输入电话"></el-input>
                    </el-form-item>
                    <el-form-item label="省份" prop="senderOptions">
                        <el-cascader
                                :options="options"
                                placeholder="请选择地区"
                                v-model="senderOptions"
                                @change="handleProvinceChange"
                            >
                        </el-cascader>
                    </el-form-item>
                    <el-form-item label="地址" prop="address">
                        <el-input v-model="infoForm.address" placeholder="请输入具体地址"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onSaveSubmit">确定保存</el-button>
                    </el-form-item>
                </el-form>

            </div>
            <div class="form-table-box">
                <el-form label-width="120px">
                    <el-form-item label="使用中的快递">
                        <el-table :data="tableData" style="width: 100%" border stripe>
                            <el-table-column prop="name" label="名字"></el-table-column>
                            <el-table-column prop="code" label="代号"></el-table-column>
                            <!-- <el-table-column prop="CustomerName" label="客户编号"></el-table-column>
                            <el-table-column prop="MonthCode" label="月结账号"></el-table-column> -->
                            <el-table-column label="操作" width="170">
                                <template slot-scope="scope">
                                    <el-button size="small" @click="handleRowEdit(scope.$index, scope.row)">编辑
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form-item>
                </el-form>
            </div>

        </div>
    </div>
</template>

<script>

    export default {
        data() {
            return {
                infoForm: {
                    id: "",
                    auto_delivery: 0,
                    name: "",
                    tel: "",
                    province_id: "",
                    province_name: "",
                    city_id: "",
                    city_name: "",
                    district_id: "",
                    district_name: "",
                    address: "",
                },
                page: 1,
                total: 0,
                filterForm: {
                    name: ''
                },
                tableData: [],
                options: [],
                senderOptions:[],
                infoRules: {
                    name: [
                        {required: true, message: '请输入寄件人姓名', trigger: 'blur'},
                    ],
                    tel: [
                        {required: true, message: '请输入寄件人电话', trigger: 'blur'},
                    ],
                    address: [
                        {required: true, message: '请输入寄件人具体地址', trigger: 'blur'},
                    ],
                }
            }
        },
        methods: {
            getAllRegion() {
                let that = this;
                this.axios.get('order/getAllRegion').then((response) => {
                    this.options = response.data.data;
                })
            },

            handleRowEdit(index, row) {
                this.$router.push({name: 'shipper_add', query: {id: row.id}})
            },
            onSaveSubmit() {
                if (this.senderOptions.length != 3) {
                    this.$message({
                        type: 'warning',
                        message: '请选择省份'
                    })
                    return;
                }

                this.$refs['infoForm'].validate((valid) => {
                    if (valid) {
                        this.axios.post('admin/storeShipperSettings', this.infoForm).then((response) => {
                            if (response.data.errno === 0) {
                                this.$message({
                                    type: 'success',
                                    message: '保存成功'
                                });
                            }
                            else {
                                this.$message({
                                    type: 'error',
                                    message: '保存失败'
                                })
                            }
                        })
                    } else {
                        return false;
                    }
                });
            },
            getList() {
                this.axios.get('shipper').then((response) => {
                    this.infoForm.id = response.data.data.set.id;
                    this.infoForm.auto_delivery = response.data.data.set.auto_delivery;
                    this.infoForm.name = response.data.data.set.name;
                    this.infoForm.tel = response.data.data.set.tel;
                    this.infoForm.province_id = response.data.data.set.province_id;
                    this.infoForm.province_name = response.data.data.set.province_name;
                    this.infoForm.city_id = response.data.data.set.city_id;
                    this.infoForm.city_name = response.data.data.set.city_name;
                    this.infoForm.district_id = response.data.data.set.district_id;
                    this.infoForm.district_name = response.data.data.set.district_name;
                    this.infoForm.address = response.data.data.set.address;

                    this.tableData = response.data.data.info;
                    this.senderOptions.push(
                        this.infoForm.province_id,
                        this.infoForm.city_id,
                        this.infoForm.district_id
                    )
                })
            },

            handleProvinceChange(value) {
                this.senderOptions = value;

                this.infoForm.province_id = this.senderOptions[0];
                this.infoForm.city_id = this.senderOptions[1];
                this.infoForm.district_id = this.senderOptions[2];

                // 将省市区id转成name 
                for (let item of this.options) {
                    if (item.value == this.infoForm.province_id) {
                        this.infoForm.province_name = item.label;
                        for (let item1 of item.children) {
                            if (item1.value == this.infoForm.city_id) {
                                this.infoForm.city_name = item1.label;
                                for (let item2 of item1.children) {
                                    if (item2.value == this.infoForm.district_id) {
                                        this.infoForm.district_name = item2.label;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        components: {},
        mounted() {
            this.getList();
            this.getAllRegion();
        }
    }
</script>
<style scoped>
    .form-table-box {
        border: 1px solid #f1f1f1;
        margin-bottom: 20px;
    }
</style>
