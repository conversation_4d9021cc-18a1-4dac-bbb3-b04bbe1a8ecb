/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-16 00:07:13
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-26 00:35:27
 * @FilePath: /petshop-admin/src/main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import ElementUI, { Loading } from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import locale from 'element-ui/lib/locale/lang/en' // lang i18n
import request from './api/request'

import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'

// set ElementUI lang to EN
Vue.use(ElementUI, { locale })

Vue.config.productionTip = false
Vue.prototype.axios = request;

router.beforeEach((to, from, next) => {

  let token = localStorage.getItem('token') || '';

  if (!token && to.name !== 'login') {
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
  } else {
    next()
  }
});

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
