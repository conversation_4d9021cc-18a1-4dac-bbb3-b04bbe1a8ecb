/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-26 00:23:31
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-22 01:27:58
 * @FilePath: /petshop-admin/src/api/request.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import axios from 'axios';
import { Loading, Message } from 'element-ui'
import router from '../router/index.js'
import api from '../config/api'

const instance = axios.create({ 
    baseURL : api.rootUrl,
    timeout : 60000 , 
});

let loadingInstance = null;
// 请求拦截器
instance.interceptors.request.use(
    (config) => {
        let token = localStorage.getItem('token') || '';
        config.headers['x-auth-token'] = token;
        // 在请求发送之前显示 loading
        // 当url == /order/recentOrderCount 不显示loading
        if(config.url.indexOf('/order/recentOrderCount') == -1){
            loadingInstance = Loading.service({
                lock: true,
                text: '加载中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)',
            });
        }
        return config;
    },
    (error) => {
        // 请求错误时隐藏 loading
        if (loadingInstance) {
            loadingInstance.close();
        }
        return Promise.reject(error);
    }
);
// 响应拦截器
instance.interceptors.response.use(
    (response) => {
        // 响应成功时隐藏 loading
        if (loadingInstance) {
            loadingInstance.close();
        }
        if (response.data.errno == 401) {
            Message.error("登录已过期，请重新登录");
            // 强制跳转到登录页面
            localStorage.removeItem('token');
            setTimeout(() => {
                router.push('/login');
            }, 500);
        }else{
            if (response.data.errno != 0) {
                Message.error(response.data.errmsg);
            }
            return response;
        }
    },
    (error) => {
        // 响应错误时隐藏 loading
        if (loadingInstance) {
            loadingInstance.close();
        }
        Message.error("加载失败，请重试");
        return Promise.reject(error);
    }
);

export default instance;