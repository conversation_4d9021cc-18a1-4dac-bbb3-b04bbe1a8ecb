/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-16 00:07:13
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-15 01:25:20
 * @FilePath: /petshop-admin/src/config/api.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 本地地址
// const rootUrl = 'http://127.0.0.1:8360/admin/';
// 测试地址
const rootUrl = process.env.VUE_APP_BASE_API;
// 生产地址
// const rootUrl = 'http://*************:8360/admin/';

const api = {
    rootUrl : rootUrl,
    // express: {
    //     // 快递物流信息查询使用的是快递鸟接口，申请地址：http://www.kdniao.com/
    //     appid: '123', // 对应快递鸟用户后台 用户ID
    //     appkey: '123123', // 对应快递鸟用户后台 API key
    //     request_url: 'http://api.kdniao.cc/Ebusiness/EbusinessOrderHandle.aspx'
    // },
	// 4.19更新，物流查询不需要以上配置，只需要在server的config配置阿里云物流接口就可以
};

export default api
